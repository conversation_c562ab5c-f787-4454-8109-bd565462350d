const { PrismaClient, SmartLeadStatus } = require("@prisma/client");
const prisma = new PrismaClient();
const axios = require("axios");
const cron = require("node-cron");
const Bottleneck = require("bottleneck");
const { cleanReply } = require("../../utils/replyMailParser");
const { adaptiveSleep } = require("./getLeadsData");
const { saveClientsFromSmartLeads } = require("./saveClientsFromSmartLeads");
const { getCampaignData } = require("./getCampaignData");
const { getLeadCategory } = require("./getLeadCategory");
require("dotenv").config();

const SMART_LEADS_API_KEY = process.env.SMART_LEADS_API_KEY;
const API_DELAY = 1000;

async function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

cron.schedule(
  "0 5 * * *", // Runs every day at 5:00 AM
  async () => {
    console.log("Cron job started at", new Date().toISOString());
    try {
      await emailThreadUpdater();
      console.log(
        "Cron job finished successfully at",
        new Date().toISOString()
      );
    } catch (error) {
      console.error("Cron job failed:", error);
    }
  },
  {
    timezone: "Asia/Kolkata",
    runOnInit: false,
  }
);

const limiter = new Bottleneck({
  reservoir: 200,
  reservoirRefreshAmount: 200,
  reservoirRefreshInterval: 60 * 1000,
  maxConcurrent: 5,
});

async function fetchLeadsSince(campaignId, fromTime, maxRetries = 5) {
  const MAX_LIMIT = 100;
  const leadsMap = new Map();
  const created_at_gt = fromTime.toISOString();
  const event_time_gt = fromTime.toISOString().split("T")[0];
  let offset = 0;

  async function safeFetch(params) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const res = await limiter.schedule(() =>
          axios.get(
            `https://server.smartlead.ai/api/v1/campaigns/${campaignId}/leads`,
            {
              params: {
                api_key: SMART_LEADS_API_KEY,
                ...params,
              },
            }
          )
        );
        await adaptiveSleep(res.headers);
        return res.data.data;
      } catch (err) {
        const status = err.response?.status;
        const errMsg = err.response?.data?.error;

        if (status === 502 && attempt < maxRetries) {
          console.warn(`⚠️ 502 Retry #${attempt} after ${1000 * attempt}ms`);
          await sleep(1000 * attempt);
        } else if (status === 404 || errMsg?.includes("Campaign not found")) {
          console.warn(`❌ Campaign ${campaignId} not found or 404, skipping.`);
          return null;
        } else {
          console.error(
            `❌ Unexpected error on fetch attempt #${attempt}:`,
            err.message
          );
          await sleep(API_DELAY);
        }
      }
    }
    return null;
  }

  // Fetch created leads
  while (true) {
    const leads = await safeFetch({ created_at_gt, offset, limit: MAX_LIMIT });
    if (!leads?.length) break;
    leads.forEach((lead) => leadsMap.set(lead.campaign_lead_map_id, lead));
    offset += MAX_LIMIT;
  }

  // Fetch updated leads
  offset = 0;
  while (true) {
    const leads = await safeFetch({ event_time_gt, offset, limit: MAX_LIMIT });
    if (!leads?.length) break;
    leads.forEach((lead) => leadsMap.set(lead.campaign_lead_map_id, lead));
    offset += MAX_LIMIT;
  }

  return Array.from(leadsMap.values());
}

async function emailThreadUpdater() {
  await saveClientsFromSmartLeads();
  await getCampaignData();
  await getLeadCategory();
  try {
    const campaigns = await prisma.campaign.findMany();

    for (const campaign of campaigns) {
      let leads = [];

      try {
        const jobRun = await prisma.jobRun.findUnique({
          where: { jobName: "emailThreadUpdater" },
        });

        const fromTime =
          jobRun?.lastRunAt || new Date(Date.now() - 24 * 60 * 60 * 1000);
        console.log(
          `📨 Fetching leads for campaign ${campaign.campaignId} since ${fromTime}`
        );

        leads = await fetchLeadsSince(campaign.campaignId, fromTime);
        if (!leads?.length) {
          console.warn(`⚠️ No leads found for campaign ${campaign.campaignId}`);
          continue;
        }
      } catch (campaignError) {
        console.error(
          `❌ Skipping campaign ${campaign.campaignId} due to error:`,
          campaignError.message
        );
        continue;
      }

      let successfulUpserts = 0;
      let failedUpserts = 0;

      for (const lead of leads) {
        try {
          const leadData = {
            campaignLeadMapId: lead.campaign_lead_map_id,
            leadId: lead.lead.id.toString(),
            email: lead.lead.email,
            website: lead.lead.website,
            campaignId: campaign.id,
            leadStatus: lead.status,
            lead_category_id: lead.lead_category_id,
          };

          const upsertedLead = await prisma.smartLead_Lead.upsert({
            where: { campaignLeadMapId: leadData.campaignLeadMapId },
            update: leadData,
            create: leadData,
          });

          const messageHistoryUrl = `https://server.smartlead.ai/api/v1/campaigns/${campaign.campaignId}/leads/${lead.lead.id}/message-history?api_key=${SMART_LEADS_API_KEY}`;
          let messageHistoryResponse;

          try {
            messageHistoryResponse = await limiter.schedule(() =>
              axios.get(messageHistoryUrl)
            );
            await adaptiveSleep(messageHistoryResponse.headers);
          } catch (error) {
            const status = error.response?.status;

            if (status === 404) {
              console.warn(
                `⚠️ Lead ID ${lead.lead.id} is MISSING (404). Marking as MISSING.`
              );
              await prisma.smartLead_Lead.update({
                where: { campaignLeadMapId: lead.campaign_lead_map_id },
                data: { status: SmartLeadStatus.MISSING },
              });
              failedUpserts++;
              continue; // skip this lead and move to the next
            } else {
              console.error(
                `❌ Error fetching message history for Lead ID ${lead.lead.id}:`,
                error.message
              );

              try {
                await prisma.smartLead_Lead.update({
                  where: { campaignLeadMapId: lead.campaign_lead_map_id },
                  data: { status: SmartLeadStatus.FAILED },
                });
              } catch (markError) {
                console.warn(
                  `⚠️ Failed to mark lead as FAILED:`,
                  markError.message
                );
              }

              failedUpserts++;
              await sleep(API_DELAY);
              continue; // skip this lead due to error
            }
          }

          const messageHistory = messageHistoryResponse.data;

          if (!messageHistory.history?.length) {
            console.log(`📭 No emails for Lead ID: ${lead.lead.id}`);
            successfulUpserts++;
            continue;
          }

          for (const email of messageHistory.history) {
            const matchId = `${lead.lead.id}_${campaign.campaignId}`;
            await prisma.email.upsert({
              where: {
                messageId_time: {
                  messageId: email.message_id,
                  time: new Date(email.time),
                },
              },
              update: {
                subject: email.subject?.trim() || "Re: Previous Conversation",
                body: email.email_body?.trim()
                  ? cleanReply(email.email_body)
                  : "This email has no body content.",
                type: email.type || "Undefined",
                toEmailID: email.to || "Undefined",
                fromEmailID: email.from || "Undefined",
                campaingId: campaign.campaignId,
                email_seq_number: email.email_seq_number,
                open_count: email.open_count,
                click_count: email.click_count,
                click_details: email.click_details,
              },
              create: {
                matchId,
                campaingId: campaign.campaignId,
                leadId: upsertedLead.id,
                messageId: email.message_id,
                subject: email.subject?.trim() || "Re: Previous Conversation",
                body:
                  email.email_body?.trim() || "This email has no body content.",
                type: email.type || "Undefined",
                toEmailID: email.to || "Undefined",
                fromEmailID: email.from || "Undefined",
                email_seq_number: email.email_seq_number,
                open_count: email.open_count,
                click_count: email.click_count,
                click_details: email.click_details,
                time: email.time ? new Date(email.time) : new Date(),
              },
            });

            console.log(`✅ Email upserted: ${email.message_id}`);
          }

          successfulUpserts++;
        } catch (leadError) {
          console.error(
            `❌ Error with lead ${lead.lead?.id}:`,
            leadError.message
          );

          try {
            await prisma.smartLead_Lead.update({
              where: { campaignLeadMapId: lead.campaign_lead_map_id },
              data: { status: SmartLeadStatus.FAILED },
            });
          } catch (markError) {
            console.warn(
              `⚠️ Failed to mark lead as FAILED:`,
              markError.message
            );
          }

          failedUpserts++;
          await sleep(API_DELAY);
        }
      }

      console.log(
        `✅ Campaign ${campaign.campaignId} complete. Success: ${successfulUpserts}, Failed: ${failedUpserts}`
      );
    }

    await prisma.jobRun.upsert({
      where: { jobName: "emailThreadUpdater" },
      update: { lastRunAt: new Date() },
      create: {
        jobName: "emailThreadUpdater",
        lastRunAt: new Date(),
      },
    });

    console.log("📌 Job completed and lastRunAt updated.");
  } catch (error) {
    console.error("❌ emailThreadUpdater crashed:", error);
  }
}

module.exports = { emailThreadUpdater };
