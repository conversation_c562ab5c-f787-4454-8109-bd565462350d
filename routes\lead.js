const express = require("express");
const router = express.Router();
const multer = require("multer");
const upload = multer({ dest: "uploads/" });
const converter = require("json-2-csv");
const { userAuth } = require("../middlewares/jwt");
const { processCSV } = require("../middlewares/processCsv");
const prisma = require("../database/prisma/getPrismaClient");
const { appendLeadsEmails } = require("../utils/appendLeadsEmails");
const {
  createSheetFromCsv,
  processZipToSheet,
  createMultiSheetFromCsvs,
} = require("../utils/googleSheetUtils");
const {
  generateJobExport,
  generateJobExportWithZip,
} = require("../utils/leadExportUtils");
const path = require("path");
const fs = require("fs");
const {
  getJobMetrics,
  processJobWithMetrics,
} = require("../utils/jobMetricsUtils");
const { adminAuth } = require("../middlewares/jwt");
const pricingConfig = require("../config/pricing");
/**
 * @swagger
 * /api/lead/prospect:
 *   post:
 *     summary: Process a CSV file for prospect data
 *     description: |
 *       This allows prospect data to be updated using the process csv method
 *
 *       Download sample CSV file:
 *       - [Download sample prospect CSV](/examples/input_lead_type.csv)
 *     tags: [Leads]
 *     parameters:
 *       - in: query
 *         name: skiperrorrows
 *         schema:
 *           type: boolean
 *           default: false
 *         description: When set to true, rows with errors will be skipped and processing will continue. When false, processing will stop on first error.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file to process with the following structure:
 *
 *                   - "Amazon Seller ID" or "amazon_seller_id": Amazon seller identifier (required)
 *                   - "Website" or "company_website" or "website": Company website URL
 *                   - "Full Name" or "full_name" or "person_name" or "Person Name": Contact person's full name
 *                   - "LinkedIn Link" or "linkedin_url" or "person_linkedin" or "contact LinkedIn" or "Person Linkedin": LinkedIn profile URL
 *                   - "Company Website Full" or "company_website" or "apollo_website" or "Apollo Website": Full company website URL
 *                   - "Phone #" or "phone" or "Phone": Contact phone number
 *                   - "Email" or "eMail" or "email": Contact email address
 *                   - "Title" or "job_title" or "Job Title": Job title of the contact
 *                   - "Contact Location" or "contact_location" or "Contact City": Location of the contact
 *                   - "Company Name" or "company" or "apollo_company_name" or "Apollo Company Name": Company name
 *                   - "SmartScout Country" or "smartscout_country" or "Smartscout Country": Country from SmartScout
 *                   - "Source" or "source" or "lookup_source" or "Lookup Source": Source of the data
 *                   - "Email Status" or "email_status" or "eMail Status": Status of the email
 *                   - "Address" or "address": Physical address
 *                   - "Pincode" or "pincode": Postal code
 *
 *                   Note: For improved data handling and seller group integration, consider using
 *                   the /api/lead/amazon_prospect endpoint which supports the enhanced AmazonProspect
 *                   model with better seller group management and cleaner data structure.
 *
 *                   Example prospect CSV:
 *                   Amazon Seller ID,Website,Full Name,LinkedIn Link,Email,Phone #,Title,Company Name
 *                   A1B2C3D4E5,https://acme.com,John Doe,https://linkedin.com/in/johndoe,<EMAIL>,************,CEO,Acme Inc
 *                   F6G7H8I9J0,https://xyz-corp.com,Jane Smith,https://linkedin.com/in/janesmith,<EMAIL>,************,CTO,XYZ Corp
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Data uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data uploaded successfully"
 *       403:
 *         description: Returns error CSV file
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               row,error_message,original_data
 *               2,"Missing required field: Amazon Seller ID","{\"Website\":\"https://missing-id.com\",\"Full Name\":\"Missing ID\"}"
 *               4,"Invalid email format","{\"Amazon Seller ID\":\"X1Y2Z3\",\"Full Name\":\"Invalid Email\",\"Email\":\"not-an-email\"}"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="ERROR-input_file.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'

 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */

/**
 * @swagger
 * /api/lead/matching:
 *   post:
 *     summary: Process a CSV file for matching data
 *     description: |
 *       This allows matching data to be updated using the process csv method
 *
 *       Download sample CSV file:
 *       - [Download sample matching CSV](/examples/input_matching_lead_type.csv)
 *     tags: [Leads]
 *     parameters:
 *       - in: query
 *         name: skiperrorrows
 *         schema:
 *           type: boolean
 *           default: false
 *         description: When set to true, rows with errors will be skipped and processing will continue. When false, processing will stop on first error.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file to process with the following structure:
 *
 *                   - "Email" or "email" or "eMail": Contact email address (required)
 *                   - "Status": Status of the matching
 *                   - "Client": Client name
 *
 *                   Example matching CSV:
 *                   Email,Status,Client
 *                   <EMAIL>,Active,Client A
 *                   <EMAIL>,Pending,Client B
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Data uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data uploaded successfully"
 *       403:
 *         description: Returns error CSV file
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               row,error_message,original_data
 *               2,"Missing required field: Email","{\"Status\":\"Active\",\"Client\":\"Client A\"}"
 *               4,"Invalid email format","{\"Email\":\"not-an-email\",\"Status\":\"Pending\",\"Client\":\"Client B\"}"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="ERROR-input_file.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'

 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */

router.post(
  "/api/lead/:type",
  adminAuth,
  upload.single("csvFile"),
  async (req, res) => {
    try {
      const csvFilePath = req.file.path;
      const originalFileName = req.file.originalname;
      const { type } = req.params;
      const skipErrorRows = req.query.skiperrorrows === "true";
      if (!["prospect", "matching", "amazon_prospect"].includes(type)) {
        return res.status(400).json({ error: "Invalid type" });
      }

      const result = await processCSV(csvFilePath, type, null, skipErrorRows);

      if (result.length > 0) {
        const csvContent = converter.json2csv(result);

        // Set the filename for the CSV file
        const inputCsvFileName = `ERROR-${originalFileName}.csv`;

        // Send the CSV file in response
        res.setHeader(
          "Content-Disposition",
          `attachment; filename=${inputCsvFileName}`
        );
        res.setHeader("Content-Type", "text/csv");
        res.status(403).send(csvContent);
      } else {
        console.log("Data uploaded successfully");
        res.json({ message: "Data uploaded successfully" });
      }
    } catch (error) {
      console.error("Error Stack:", error.stack);
      console.error("Error posting job:", error.message);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

/**
 * @swagger
 * /api/lead/insert/company:
 *   post:
 *     summary: Insert company data from CSV file
 *     description: |
 *       This endpoint allows you to process a CSV file to insert new company records into the database. By default blanks values are skipped and if there are errors a csv with the errors listed is returned. Errors include:
 *         - Missing required fields (Amazon Seller ID)
 *         - Website present but website status is empty
 *         - Domain already associated with a different Amazon Seller ID
 *         - Domain associated with a different Amazon Seller ID in the same file
 *
 *       Note: For Amazon Seller data, use the /api/lead/insert/amazon_seller endpoint which supports
 *       enhanced fields including avg_price, number_reviews_lifetime, number_reviews_30days,
 *       suspension tracking (is_suspended, last_suspended_date), detailed address fields
 *       (street, city, adr_state, adr_country, adr_zip_code), and improved seller group management.
 *
 *       Download sample CSV file:
 *       - [Download sample insert company CSV](/examples/input_lead_operation_type_insert.csv)
 *     tags: [Leads]
 *     parameters:
 *       - in: query
 *         name: skiperrorrows
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to skip rows with errors
 *       - in: query
 *         name: updateBlankValues
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to allow blank rows or values
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file to process with the following structure for company data:
 *
 *                   Required fields:
 *                   - "Amazon Seller ID" or "amazon_seller_id" or "Seller ID": Amazon seller identifier (required)
 *
 *                   Optional fields:
 *                   - "Name" or "name" or "Seller Name": Name of the seller
 *                   - "Primary Category ID" or "primary_category_id": Primary category identifier
 *                   - "Primary Category" or "primary_category": Primary category name
 *                   - "Primary Sub Category" or "primary_sub_category": Primary sub-category
 *                   - "Estimate Sales" or "estimate_sales": Estimated sales figure
 *                   - "Percent FBA" or "percent_fba": Percentage of Fulfillment by Amazon
 *                   - "Number Winning Brands" or "number_winning_brands": Number of winning brands
 *                   - "Number ASINs" or "number_asins": Number of ASINs
 *                   - "Number Top ASINs" or "number_top_asins": Number of top ASINs
 *                   - "State" or "state": State location
 *                   - "Country" or "country": Country location
 *                   - "Business Name" or "business_name": Business name
 *                   - "Number Brands 1000" or "number_brands_1000": Number of brands in top 1000
 *                   - "MoM Growth" or "mom_growth": Month-over-Month growth
 *                   - "MoM Growth Count" or "mom_growth_count": Month-over-Month growth count
 *                   - "Started Selling Date" or "started_selling_date": Date started selling
 *                   - "SmartScout Country" or "smartscout_country" or "Smartscout Country": Country from SmartScout
 *                   - "Website" or "website": Company website URL
 *                   - "Website Status" or "website_status": Status of the website
 *                   - "Find Website Batch" or "find_website_batch": Website batch identifier
 *                   - "Employee Count" or "employee_count": Number of employees
 *                   - "Company LinkedIn" or "company_linkedin": Company LinkedIn URL
 *                   - "Company Twitter" or "company_twitter": Company Twitter URL
 *                   - "Company Facebook" or "company_fb": Company Facebook URL
 *                   - "Company Location" or "company_location": Company location
 *                   - "Lookup Source" or "lookup_source": Source of the data
 *                   - "Address" or "address" or "sellerAddress" or "company_address": Company address
 *                   - "Pincode" or "pincode" or "sellerPincode" or "company_pincode": Company postal code
 *
 *                   Example company CSV:
 *                   Amazon Seller ID,Name,Business Name,Website,Website Status,Country,Address
 *                   A1B2C3D4E5,Acme Inc,Acme Corporation,https://acme.com,Final Correct,US,123 Main St
 *                   F6G7H8I9J0,XYZ Corp,XYZ Industries,https://xyz-corp.com,Pending,UK,456 Oak Ave
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Data uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data uploaded successfully"
 *       403:
 *         description: CSV file containing error rows
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               row,error_message,Amazon Seller ID,Name,Business Name,Website,Website Status
 *               2,"Missing required field: Amazon Seller ID","","Acme Inc","Acme Corporation","https://acme.com","Final Correct"
 *               4,"Website present but website_status is empty","K1L2M3N4O5","XYZ Corp","XYZ Industries","https://xyz-corp.com",""
 *               5,"Domain already associated with a different Amazon Seller ID","P6Q7R8S9T0","ABC Ltd","ABC Limited","https://acme.com","Final Correct"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="ERROR-input_file.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       400:
 *         description: Invalid type or operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid type"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'

 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */

/**
 * @swagger
 * /api/lead/update/company:
 *   post:
 *     summary: Update company data from CSV file
 *     description: |
 *       This endpoint allows you to process a CSV file to update existing company records in the database. By default blanks values are skipped and if there are errors a csv with the errors listed is returned. Errors include:
 *         - Missing required fields (Amazon Seller ID)
 *         - Website present but website status is empty
 *         - Domain already associated with a different Amazon Seller ID
 *         - Domain associated with a different Amazon Seller ID in the same file
 *         - Cannot perform update - record would be inserted as new company
 *         - Domain does not match with the existing record when website status is "Final Correct"
 *
 *       Note: For Amazon Seller data, use the /api/lead/update/amazon_seller endpoint which supports
 *       enhanced fields including avg_price, number_reviews_lifetime, number_reviews_30days,
 *       suspension tracking (is_suspended, last_suspended_date), detailed address fields
 *       (street, city, adr_state, adr_country, adr_zip_code), and improved seller group management.
 *
 *       Download sample CSV file:
 *       - [Download sample update company CSV](/examples/input_lead_operation_type_update_company.csv)
 *     tags: [Leads]
 *     parameters:
 *       - in: query
 *         name: skiperrorrows
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to skip rows with errors
 *       - in: query
 *         name: updateBlankValues
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to allow blank rows or values
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: |
 *                   CSV file to process with the following structure for company data:
 *
 *                   Required fields:
 *                   - "Amazon Seller ID" or "amazon_seller_id" or "Seller ID": Amazon seller identifier (required)
 *
 *                   Optional fields:
 *                   - "Name" or "name" or "Seller Name": Name of the seller
 *                   - "Primary Category ID" or "primary_category_id": Primary category identifier
 *                   - "Primary Category" or "primary_category": Primary category name
 *                   - "Primary Sub Category" or "primary_sub_category": Primary sub-category
 *                   - "Estimate Sales" or "estimate_sales": Estimated sales figure
 *                   - "Percent FBA" or "percent_fba": Percentage of Fulfillment by Amazon
 *                   - "Number Winning Brands" or "number_winning_brands": Number of winning brands
 *                   - "Number ASINs" or "number_asins": Number of ASINs
 *                   - "Number Top ASINs" or "number_top_asins": Number of top ASINs
 *                   - "State" or "state": State location
 *                   - "Country" or "country": Country location
 *                   - "Business Name" or "business_name": Business name
 *                   - "Number Brands 1000" or "number_brands_1000": Number of brands in top 1000
 *                   - "MoM Growth" or "mom_growth": Month-over-Month growth
 *                   - "MoM Growth Count" or "mom_growth_count": Month-over-Month growth count
 *                   - "Started Selling Date" or "started_selling_date": Date started selling
 *                   - "SmartScout Country" or "smartscout_country" or "Smartscout Country": Country from SmartScout
 *                   - "Website" or "website": Company website URL
 *                   - "Website Status" or "website_status": Status of the website
 *                   - "Find Website Batch" or "find_website_batch": Website batch identifier
 *                   - "Employee Count" or "employee_count": Number of employees
 *                   - "Company LinkedIn" or "company_linkedin": Company LinkedIn URL
 *                   - "Company Twitter" or "company_twitter": Company Twitter URL
 *                   - "Company Facebook" or "company_fb": Company Facebook URL
 *                   - "Company Location" or "company_location": Company location
 *                   - "Lookup Source" or "lookup_source": Source of the data
 *                   - "Address" or "address" or "sellerAddress" or "company_address": Company address
 *                   - "Pincode" or "pincode" or "sellerPincode" or "company_pincode": Company postal code
 *
 *                   Example company CSV:
 *                   Amazon Seller ID,Name,Business Name,Website,Website Status,Country,Address
 *                   A1B2C3D4E5,Acme Inc,Acme Corporation,https://acme.com,Final Correct,US,123 Main St
 *                   F6G7H8I9J0,XYZ Corp,XYZ Industries,https://xyz-corp.com,Pending,UK,456 Oak Ave
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Data uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data uploaded successfully"
 *       403:
 *         description: CSV file containing error rows
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               row,error_message,Amazon Seller ID,Name,Business Name,Website,Website Status
 *               2,"Missing required field: Amazon Seller ID","","Acme Inc","Acme Corporation","https://acme.com","Final Correct"
 *               4,"Website present but website_status is empty","K1L2M3N4O5","XYZ Corp","XYZ Industries","https://xyz-corp.com",""
 *               5,"Cannot perform update - record does not exist","P6Q7R8S9T0","ABC Ltd","ABC Limited","https://abc.com","Final Correct"
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             description: Attachment header with filename
 *             example: attachment; filename="ERROR-input_file.csv"
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *       400:
 *         description: Invalid type or operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid type"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

router.post(
  "/api/lead/:operation/:type",
  adminAuth,
  upload.single("csvFile"),
  async (req, res) => {
    try {
      const csvFilePath = req.file.path;
      const originalFileName = req.file.originalname;
      const { type, operation } = req.params;
      const skipErrorRows = req.query.skiperrorrows === "true";
      const allowBlankRows =
        req.query.allowblankrows === "true" ||
        req.query.updateBlankValues?.toLowerCase() === "true";

      if (!["company", "amazon_seller"].includes(type)) {
        return res.status(400).json({ error: "Invalid type" });
      }
      if (!["insert", "update"].includes(operation)) {
        return res.status(400).json({ error: "Invalid operation" });
      }

      const result = await processCSV(
        csvFilePath,
        type,
        operation,
        skipErrorRows,
        allowBlankRows
      );

      if (result.length > 0) {
        const csvContent = converter.json2csv(result);

        // Set the filename for the CSV file
        const inputCsvFileName = `ERROR-${originalFileName}.csv`;

        // Send the CSV file in response
        res.setHeader(
          "Content-Disposition",
          `attachment; filename=${inputCsvFileName}`
        );
        res.setHeader("Content-Type", "text/csv");
        res.status(403).send(csvContent);
      } else {
        console.log("Data uploaded successfully");
        res.json({ message: "Data uploaded successfully" });
      }
    } catch (error) {
      console.error("Error Stack:", error.stack);
      console.error("Error posting job:", error.message);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);
/**
 * @swagger
 * /api/lead/reply-automation/{action}/{slug}:
 *   post:
 *     summary: Handle lead automation actions like email tracking and category updates
 *     description: |
 *       Processes automation events for leads, such as tracking emails sent/replied and updating lead categories.
 *
 *       **Note: This API does not require authentication** - it is designed to be called directly by automation systems.
 *     tags: [Leads]
 *     security: []  # This explicitly indicates no security requirements
 *     parameters:
 *       - in: path
 *         name: action
 *         required: true
 *         schema:
 *           type: string
 *           enum: [email-sent, email-replied, lead-category-updated]
 *         description: The type of automation action
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Client name for the lead
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               category:
 *                 type: string
 *                 example: "qualified-lead"
 *                 description: Reply sentiment or category (for lead-category-updated)
 *               campaign_name:
 *                 type: string
 *                 example: "Summer 2023 Outreach"
 *                 description: Name of the campaign
 *               from_email:
 *                 type: string
 *                 example: "<EMAIL>"
 *                 description: Email address of the prospect (required)
 *               lead_data:
 *                 type: object
 *                 properties:
 *                   email:
 *                     type: string
 *                     example: "<EMAIL>"
 *                     description: Alternative way to provide email
 *               to_name:
 *                 type: string
 *                 example: "Client A"
 *                 description: Client name
 *               client_id:
 *                 type: string
 *                 example: "Client A"
 *                 description: Alternative way to provide client name
 *               event_timestamp:
 *                 type: string
 *                 format: date-time
 *                 example: "2023-10-15T14:30:00Z"
 *                 description: When the action occurred
 *               last_reply:
 *                 type: object
 *                 properties:
 *                   time:
 *                     type: string
 *                     format: date-time
 *                     example: "2023-10-15T14:30:00Z"
 *                     description: Timestamp of the last reply
 *               sent_message:
 *                 type: object
 *                 properties:
 *                   text:
 *                     type: string
 *                     example: "Hello, I'd like to discuss our services..."
 *                     description: Content of the sent message
 *                   time:
 *                     type: string
 *                     format: date-time
 *                     example: "2023-10-15T14:30:00Z"
 *                     description: When the message was sent
 *               reply_message:
 *                 type: object
 *                 properties:
 *                   text:
 *                     type: string
 *                     example: "Thank you for reaching out. I'm interested..."
 *                     description: Content of the reply message
 *                   time:
 *                     type: string
 *                     format: date-time
 *                     example: "2023-10-15T15:45:00Z"
 *                     description: When the reply was received
 *     responses:
 *       200:
 *         description: Action processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 replySentiment:
 *                   type: string
 *                   nullable: true
 *                   example: "qualified-lead"
 *                   description: The sentiment or category of the reply
 *                 campaignName:
 *                   type: string
 *                   nullable: true
 *                   example: "Summer 2023 Outreach"
 *                   description: The name of the campaign
 *                 email:
 *                   type: string
 *                   nullable: true
 *                   example: "<EMAIL>"
 *                   description: Email address of the prospect
 *                 clientName:
 *                   type: string
 *                   nullable: true
 *                   example: "Client A"
 *                   description: Name of the client
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                   nullable: true
 *                   example: "2023-10-15T14:30:00Z"
 *                   description: Timestamp of the action
 *                 emailsSent:
 *                   type: array
 *                   items:
 *                     type: object
 *                   example: []
 *                   description: Array of sent emails (populated in database but not returned)
 *                 replies:
 *                   type: array
 *                   items:
 *                     type: object
 *                   example: []
 *                   description: Array of replies (populated in database but not returned)
 *       400:
 *         description: Invalid request - missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Email is required"
 *                 status:
 *                   type: number
 *                   example: 400


 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.post("/api/lead/reply-automation/:action/:slug", async (req, res) => {
  const { action, slug } = req.params;
  const restOfData = req.body;

  console.log(`Action Type: ${action}`);
  console.log("Received Data:", restOfData);

  try {
    if (
      !["email-sent", "email-replied", "lead-category-updated"].includes(action)
    ) {
      return res.status(400).send({ error: "Invalid action type." });
    }

    if (!restOfData) {
      return res.status(400).send({ error: "No data provided." });
    }

    const result = await appendLeadsEmails(action, restOfData, slug);

    res.status(200).send(result || { message: "Data appended successfully" });
  } catch (error) {
    console.error("Error handling request:", error);

    if (error.status) {
      res.status(error.status).send({ error: error.message });
    }
    res.status(500).send({
      error: "Internal Server Error",
      details: typeof error === "string" ? error : error.message,
    });
  }
});
const {
  processLeadGeneration,
} = require("../services/generateLeads/createJob");
const {
  getAvailableSearchPatterns,
} = require("../services/generateLeads/utils/searchPatternGenerator");
/**
 * @swagger
 * /api/single-lead:
 *   post:
 *     summary: Creates a new lead generation job for a single lead
 *     tags: [Leads]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - businessName
 *             properties:
 *               useDomain:
 *                 type: string
 *                 enum: ['0', '1']
 *                 example: "1"
 *                 description: Whether to use domain for matching (1) or not (0)
 *               sellerName:
 *                 type: string
 *                 example: "Fervour Inc."
 *                 description: Name of the seller
 *               businessName:
 *                 type: string
 *                 example: "Fervour LLC"
 *                 description: Name of the business (required)
 *               country:
 *                 type: string
 *                 example: "US"
 *                 description: Country code
 *               status:
 *                 type: string
 *                 example: "success"
 *                 description: Current status
 *               sellerUrl:
 *                 type: string
 *                 example: "https://www.amazon.com/sp?seller=A2DNLH7BE81WOO"
 *                 description: URL of the seller on Amazon
 *               companyAddress:
 *                 type: string
 *                 example: "200 sw 85th ave apt 212, Pembroke Pines, FL, 33025, US"
 *                 description: Physical address of the company
 *               name:
 *                 type: string
 *                 example: "Fervour Inc."
 *                 description: Alternative name
 *               url:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["https://www.fervour.com/collections/all?srsltid=AfmBOorGIkI9h5HJc9zIOJ9tIWBTnk-kBFjyewdKgc_tDwMwxkMSbC1c","https://www.bizprofile.net/fl/pembroke-pines/fervour-llc","https://www.importgenius.com/importers/fervour-trade-inc"]
 *                 description: List of URLs associated with the business
 *               searchPattern:
 *                 type: string
 *                 enum: ['original', 'quoted_with_shop', 'unquoted_with_shop']
 *                 default: 'original'
 *                 example: "original"
 *                 description: |
 *                   Search pattern to use for SERP queries:
 *                   - original: sellerName businessName
 *                   - quoted_with_shop: "sellerName" | "businessName" shop
 *                   - unquoted_with_shop: sellerName businessName shop
 *     responses:
 *       200:
 *         description: Lead generation job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Lead generation job created successfully"
 *                 jobId:
 *                   type: integer
 *                   example: 42
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized - No token provided"
 *       403:
 *         description: Forbidden - Not an admin or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Forbidden - Admin access required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

router.post("/api/single-lead", adminAuth, async (req, res) => {
  try {
    const useDomain = req.body.useDomain === "1";
    const data = await transformData(req.body, leadSchemaMap);
    delete data.errors;
    const mode = "input_domain";
    const searchPattern = req.body.searchPattern || "original";
    const jobName =
      data.businessName || `Single Lead - ${new Date().toISOString()}`;
    const job = await processLeadGeneration(
      undefined,
      useDomain,
      mode,
      data,
      jobName,
      searchPattern
    );
    res.json({
      message: "Lead generation job created successfully",
      jobId: job.id,
    });
  } catch (error) {
    console.error("Error creating lead generation job:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});
/**
 * @swagger
 * /api/lead-generate:
 *   post:
 *     summary: Generate leads from CSV file
 *     description: |
 *       Upload a CSV file to generate leads. Normal csv uses the seller name + business name to make a serp/google search and then get a potential list of websites to find the website from. Input domain mode uses the domain from the website column to find the company. It doesn't use a google search, hence data is limited to the csv itself.
 *       - [Download sample Normal ](/examples/lead_normal.csv)
 *       - [Download sample Input domain mode csv](/examples/lead_input_domain.csv)
 *
 *       The CSV should contain columns like Seller Name, Business Name, Company Address, Seller URL, Website, Country, etc.
 *     tags: [Leads]
 *     parameters:
 *       - in: query
 *         name: mode
 *         schema:
 *           type: string
 *           enum: [inputDomain, serp]
 *         description: Mode of lead generation (inputDomain or serp)
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               csvFile:
 *                 type: string
 *                 format: binary
 *                 description: CSV file containing lead data
 *               useDomain:
 *                 type: string
 *                 enum: ['0', '1']
 *                 description: Whether to use domain (1) or not (0)
 *               searchPattern:
 *                 type: string
 *                 enum: ['original', 'quoted_with_shop', 'unquoted_with_shop']
 *                 default: 'original'
 *                 description: |
 *                   Search pattern to use for SERP queries:
 *                   - original: sellerName businessName
 *                   - quoted_with_shop: "sellerName" | "businessName" shop
 *                   - unquoted_with_shop: sellerName businessName shop
 *             required:
 *               - csvFile
 *     responses:
 *       200:
 *         description: Lead generation job created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Lead generation job created successfully"
 *                 jobId:
 *                   type: integer
 *                   example: 123
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */

router.post(
  "/api/lead-generate",
  adminAuth,
  upload.single("csvFile"),
  async (req, res) => {
    try {
      const csvFilePath = req.file.path;
      const originalFileName = req.file.originalname;
      const useDomain = req.body.useDomain === "1";
      const mode = req.query.mode === "inputDomain" ? "input_domain" : "serp";
      const searchPattern = req.body.searchPattern || "original";

      const job = await processLeadGeneration(
        csvFilePath,
        useDomain,
        mode,
        undefined,
        originalFileName,
        searchPattern
      );
      res.json({
        message: "Lead generation job created successfully",
        jobId: job.id,
      });
    } catch (error) {
      console.error("Error creating lead generation job:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

const lPrisma = require("../database/prisma/getPrismaClient");
const { transformData, leadSchemaMap } = require("../middlewares/schema");
/**
 * @swagger
 * /api/lead/export/{jobIds}:
 *   get:
 *     summary: Export lead data for specified jobs
 *     description: Exports lead data for the specified job IDs and returns a ZIP file containing multiple CSV files with different views of the data.
 *     tags: [Leads]
 *     parameters:
 *       - in: path
 *         name: jobIds
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated job IDs
 *         example: "123,456,789"
 *       - in: query
 *         name: includeDebug
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to include technical debugging information in the export. When true, additional sheets with technical details will be included.
 *     responses:
 *       200:
 *         description: Returns ZIP file with exported data
 *         content:
 *           application/zip:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: application/zip
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             example: attachment; filename="jobs-***********-export.zip"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized - No token provided"
 *       403:
 *         description: Forbidden - Not an admin or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Forbidden - Admin access required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

router.get("/api/lead/export/:jobIds", async (req, res) => {
  try {
    const jobIds = req.params.jobIds.split(",").map((id) => parseInt(id, 10));
    const includeDebug = req.query.includeDebug === "true";

    // Use the utility function to generate the export with ZIP
    const exportResult = await generateJobExportWithZip(
      jobIds,
      false,
      includeDebug
    );

    // Send the ZIP file directly to the client
    res.setHeader("Content-Type", "application/zip");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${exportResult.zipFile.zipFileName}`
    );
    res.sendFile(exportResult.zipFile.zipFilePath);
  } catch (error) {
    console.error("Error exporting job data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

const validateApiKey = (req, res, next) => {
  const apiKey = req.headers["x-api-key"];

  if (apiKey === "12345678") {
    next();
  } else {
    res.status(401).json({ error: "Invalid API key" });
  }
};
/**
 * @swagger
 * /api/lead/{jobId}:
 *   delete:
 *     summary: Delete a lead job and associated data
 *     description: Deletes a lead job and all associated data. A lead job is an existing CSV which is being currently processed (uploaded using lead-generate or single-lead).
 *     tags: [Leads]
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the job to delete
 *         example: 123
 *       - in: header
 *         name: x-api-key
 *         required: true
 *         schema:
 *           type: string
 *         description: API key for authentication
 *         example: "12345678"
 *     responses:
 *       200:
 *         description: Lead job deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Lead job and associated data deleted successfully"
 *       401:
 *         description: Invalid API key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid API key"
 *       404:
 *         description: Lead job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Lead job not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

router.delete(
  "/api/lead/:jobId",
  adminAuth,
  validateApiKey,
  async (req, res) => {
    try {
      const jobId = parseInt(req.params.jobId);
      const job = await prisma.leadJob.findUnique({
        where: {
          id: jobId,
        },
      });
      if (!job) {
        return res.status(404).json({ error: "Lead job not found" });
      }

      await prisma.leadUrl.deleteMany({
        where: {
          lead: {
            jobId: jobId,
          },
        },
      });

      await prisma.lead.deleteMany({
        where: {
          jobId: jobId,
        },
      });

      await prisma.leadJob.delete({
        where: {
          id: jobId,
        },
      });

      res.json({
        message: "Lead job and associated data deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting lead job:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

/**
 * @swagger
 * /api/lead/export-input/{jobIds}:
 *   get:
 *     summary: Export input data for specified jobs
 *     description: Exports the original input data for the specified job IDs. These are the CSVs uploaded using lead-generate or single-lead. This is used to get the result after they are processed. Can export multiple jobs at once by passing IDs as comma separated.
 *     tags: [Leads]
 *     parameters:
 *       - in: path
 *         name: jobIds
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated job IDs
 *         example: "123,456,789"
 *     responses:
 *       200:
 *         description: Returns CSV file with input data
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *             example: |
 *               "Job ID","Seller Name","Business Name","Company Address","Seller URL","Country"
 *               123,"Acme Inc","Acme Corporation","123 Main St, City, State","https://amazon.com/seller/acme","US"
 *               456,"XYZ Corp","XYZ Industries","456 Oak Ave, Town, State","https://amazon.com/seller/xyz","UK"
 *         headers:
 *           Content-Type:
 *             schema:
 *               type: string
 *             example: text/csv
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             example: attachment; filename="lead-input-***********.csv"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

router.get("/api/lead/export-input/:jobIds", adminAuth, async (req, res) => {
  try {
    const jobIds = req.params.jobIds.split(",").map((id) => parseInt(id, 10));
    const batchSize = 1000;
    let skip = 0;
    let allLeads = [];

    // Fetch leads in batches
    while (true) {
      const leads = await lPrisma.lead.findMany({
        where: { jobId: { in: jobIds } },
        select: {
          jobId: true,
          sellerName: true,
          businessName: true,
          address: true,
          sellerUrl: true,
          country: true,
          metadata: true,
        },
        take: batchSize,
        skip: skip,
      });

      if (leads.length === 0) break;
      allLeads = allLeads.concat(leads);
      skip += batchSize;
    }

    // Transform leads data for CSV
    const csvData = allLeads.map((lead) => {
      return {
        "Job ID": lead.jobId,
        "Seller Name": lead.sellerName,
        "Business Name": lead.businessName,
        "Company Address": lead.address,
        "Seller URL": lead.sellerUrl,
        Country: lead.country,
        ...lead.metadata,
      };
    });

    // Convert to CSV
    const csvContent = await converter.json2csv(csvData);

    // Set response headers for CSV download
    const fileName = `lead-input-${jobIds.join("-")}.csv`;
    res.setHeader("Content-Type", "text/csv");
    res.setHeader("Content-Disposition", `attachment; filename=${fileName}`);
    res.send(csvContent);
  } catch (error) {
    console.error("Error exporting lead input data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/lead/export-link/{jobIds}:
 *   get:
 *     summary: Generate export links for specified jobs
 *     description: Generates export links on Google Sheets for the specified job IDs. This creates a Google Sheet with the exported data and returns links to access it.
 *     tags: [Leads]
 *     parameters:
 *       - in: path
 *         name: jobIds
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated job IDs
 *         example: "123,456,789"
 *       - in: query
 *         name: includeDebug
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to include debugging information in the exports. When true, additional sheets with all technical details will be included.
 *     responses:
 *       200:
 *         description: Returns export links
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 sheetUrl:
 *                   type: string
 *                   example: ""
 *                   description: URL to the Google Sheet with exported data
 *                 sheetId:
 *                   type: string
 *                   example: "1a2b3c4d5e6f7g8h9i0j"
 *                   description: ID of the Google Sheet
 *                 sheets:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       title:
 *                         type: string
 *                         example: "Main Data"
 *                       sheetId:
 *                         type: integer
 *                         example: 0
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized - No token provided"
 *       403:
 *         description: Forbidden - Not an admin or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Forbidden - Admin access required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

router.get("/api/lead/export-link/:jobIds", adminAuth, async (req, res) => {
  try {
    const jobIds = req.params.jobIds.split(",").map((id) => parseInt(id, 10));
    const includeDebug = req.query.includeDebug === "true";

    // Use the utility function to generate the export
    const exportResult = await generateJobExport(jobIds, true, includeDebug);
    console.log(`Export result: ${JSON.stringify(exportResult)}`);
    if (jobIds.length === 1) {
      await prisma.leadJob.update({
        where: { id: jobIds[0] },
        data: {
          resultJson: exportResult,
        },
      });
    }

    // Return the result
    res.json(exportResult);
  } catch (error) {
    console.error("Error generating export links:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});
/**
 * @swagger
 * /api/lead-job-status/{jobId}:
 *   get:
 *     summary: Get status and metrics of a specific lead job
 *     description: Returns detailed status information and completion metrics for a specific lead job.
 *     tags: [Leads]
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The job ID
 *         example: 123
 *     responses:
 *       200:
 *         description: Returns job details with completion metrics and status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     isComplete:
 *                       type: boolean
 *                       example: false
 *                     statusMap:
 *                       type: object
 *                       properties:
 *                         pending:
 *                           type: integer
 *                           example: 5
 *                         srp_requested:
 *                           type: integer
 *                           example: 10
 *                         srp_failed:
 *                           type: integer
 *                           example: 2
 *                         srp_scraped:
 *                           type: integer
 *                           example: 15
 *                         processing:
 *                           type: integer
 *                           example: 8
 *                         scoring:
 *                           type: integer
 *                           example: 3
 *                         validating:
 *                           type: integer
 *                           example: 7
 *                         completed:
 *                           type: integer
 *                           example: 20
 *                         failed:
 *                           type: integer
 *                           example: 4
 *                     totalLeads:
 *                       type: integer
 *                       example: 74
 *                     completionPercentage:
 *                       type: number
 *                       format: float
 *                       example: 27.03
 *                     status:
 *                       type: string
 *                       enum: [completed, pending]
 *                       example: "pending"
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Job not found"
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized - No token provided"
 *       403:
 *         description: Forbidden - Not an admin or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Forbidden - Admin access required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.get("/api/lead-job-status/:jobId", adminAuth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.jobId, 10);

    const job = await prisma.leadJob.findUnique({
      where: { id: jobId },
    });

    if (!job) {
      return res.status(404).json({ error: "Job not found" });
    }

    const jobDetails = await processJobWithMetrics(job);

    res.json({
      success: true,
      data: jobDetails,
    });
  } catch (error) {
    console.error("Error fetching job details:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});
/**
 * @swagger
 * /api/lead-jobs:
 *   get:
 *     summary: Get all lead jobs with pagination
 *     description: Retrieves a paginated list of all lead jobs ordered by most recent first
 *     tags: [Leads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Successfully retrieved lead jobs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "Lead Job 2024-03-15"
 *                       status:
 *                         type: string
 *                         enum: [pending, in_progress, completed, failed]
 *                         example: "in_progress"
 *                       isComplete:
 *                         type: boolean
 *                         example: false
 *                       statusMap:
 *                         type: object
 *                         properties:
 *                           pending:
 *                             type: integer
 *                             example: 5
 *                           srp_requested:
 *                             type: integer
 *                             example: 10
 *                           srp_failed:
 *                             type: integer
 *                             example: 2
 *                           srp_scraped:
 *                             type: integer
 *                             example: 15
 *                           processing:
 *                             type: integer
 *                             example: 8
 *                           scoring:
 *                             type: integer
 *                             example: 3
 *                           validating:
 *                             type: integer
 *                             example: 7
 *                           completed:
 *                             type: integer
 *                             example: 20
 *                           failed:
 *                             type: integer
 *                             example: 4
 *                       totalLeads:
 *                         type: integer
 *                         example: 74
 *                       completionPercentage:
 *                         type: number
 *                         format: float
 *                         example: 27.03
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-03-15T10:30:00Z"
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-03-15T10:35:00Z"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 50
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                     totalPages:
 *                       type: integer
 *                       example: 5
 *       401:
 *         description: Unauthorized - No token provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized - No token provided"
 *       403:
 *         description: Forbidden - Not an admin or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Forbidden - Admin access required"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */
router.get("/api/lead-jobs", adminAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page || "1");
    const limit = parseInt(req.query.limit || "10");
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await prisma.leadJob.count();

    // Get paginated jobs
    const jobs = await prisma.leadJob.findMany({
      orderBy: {
        id: "desc",
      },
      skip,
      take: limit,
    });

    if (jobs.length === 0) {
      return res.json({
        success: true,
        data: [],
        pagination: {
          total: totalCount,
          page,
          limit,
          totalPages: Math.ceil(totalCount / limit),
        },
      });
    }

    // Process jobs data efficiently using the utility function
    const jobsWithCounts = await Promise.all(jobs.map(processJobWithMetrics));

    res.json({
      success: true,
      data: jobsWithCounts,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching lead jobs:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});
/**
 * @swagger
 * /api/lead-job/{jobId}:
 *   get:
 *     summary: Get details of a specific lead job
 *     tags: [Leads]
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Returns job details with completion metrics and status, status map is nubmer of leads per status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isComplete:
 *                   type: boolean
 *                 statusMap:
 *                   type: object
 *                 totalLeads:
 *                   type: integer
 *                 completionPercentage:
 *                   type: number
 *                   format: float
 *                 status:
 *                   type: string
 *                   enum: [completed, pending]
 *       404:
 *         description: Job not found
 *       500:
 *         description: Internal server error
 */
router.get("/api/lead-job/:jobId", adminAuth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.jobId, 10);

    const job = await prisma.leadJob.findUnique({
      where: { id: jobId },
    });

    if (!job) {
      return res.status(404).json({ error: "Job not found" });
    }

    const resultJson = await processJobWithMetrics(job);
    res.json(resultJson);
  } catch (error) {
    console.error("Error checking lead job status:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * @swagger
 * /api/lead/search-patterns:
 *   get:
 *     summary: Get available search patterns for lead generation
 *     description: Returns a list of available search patterns that can be used when creating lead generation jobs
 *     tags: [Leads]
 *     responses:
 *       200:
 *         description: List of available search patterns
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 patterns:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: string
 *                         description: The pattern value to use in API calls
 *                       label:
 *                         type: string
 *                         description: Human-readable label for the pattern
 *                       description:
 *                         type: string
 *                         description: Detailed description of what the pattern does
 *             example:
 *               patterns:
 *                 - value: "original"
 *                   label: "Original (sellerName businessName)"
 *                   description: "The original search pattern using seller name and business name"
 *                 - value: "quoted_with_shop"
 *                   label: "Quoted with Shop (\"sellerName\" | \"businessName\" shop)"
 *                   description: "Searches for exact phrases with OR operator and includes 'shop' keyword"
 *       500:
 *         description: Internal server error
 */
router.get("/api/lead/search-patterns", adminAuth, async (req, res) => {
  try {
    const patterns = getAvailableSearchPatterns();
    res.json({ patterns });
  } catch (error) {
    console.error("Error getting search patterns:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Export the router
module.exports = router;
