const introspector = require("../utils/prismaModelIntrospector");
const { getPreprocessor } = require("./preprocessor");

const companySchemaMap = {
  name: ["Name", "name", "Seller Name", "company_name"],
  amazon_seller_id: [
    "seller_id",
    "Amazon Seller ID",
    "amazon_seller_id",
    "Seller ID",
    "amazonSellerId",
    "SellerCountryMatching_amazon_seller_id",
  ],
  primary_category_id: [
    "Primary Category ID",
    "primary_category_id",
    "primaryCategoryIdString",
  ],
  primary_category: ["Primary Category", "primary_category"],
  primary_sub_category: [
    "Primary Sub Category",
    "primary_sub_category",
    "primarySubCategory",
  ],
  estimate_sales: ["Estimate Sales", "estimate_sales", "estimateSales"],
  percent_fba: ["Percent FBA", "percent_fba", "percentFba"],
  number_winning_brands: [
    "Number Winning Brands",
    "number_winning_brands",
    "numberWinningBrands",
  ],
  number_asins: ["Number ASINs", "number_asins", "numberAsins"],
  number_top_asins: ["Number Top ASINs", "number_top_asins"],
  state: ["State", "state"],
  country: ["Country", "country"],
  business_name: ["Business Name", "business_name"],
  number_brands_1000: [
    "Number Brands 1000",
    "number_brands_1000",
    "numberBrands1000",
  ],
  mom_growth: ["MoM Growth", "mom_growth", "moMGrowth"],
  mom_growth_count: ["MoM Growth Count", "mom_growth_count", "moMGrowthCount"],
  started_selling_date: [
    "Started Selling Date",
    "started_selling_date",
    "startedSellingDate",
  ],
  smartscout_country: [
    "SmartScout Country",
    "smartscout_country",
    "Smartscout Country",
    "marketplace",
  ],
  lookup_source: [
    "Lookup Source",
    "lookup_source",
    "lookupSource",
    "Source",
    "source",
  ],
  website: ["Website", "website"],
  domain: ["domain"],
  website_status: ["Website Status", "website_status"],
  find_website_batch: ["find_website_batch", "Find Website Batch"],
  employee_count: ["Employee Count", "employee_count"],
  company_linkedin: ["Company LinkedIn", "company_linkedin"],
  company_twitter: ["Company Twitter", "company_twitter"],
  company_fb: ["Company Facebook", "company_fb"],
  company_location: ["Company Location", "company_location"],
  company_address: ["Address", "address", "sellerAddress", "company_address"],
  company_pincode: ["Pincode", "pincode", "sellerPincode", "company_pincode"],
};

// Schema map for the new AmazonSeller table
const amazonSellerSchemaMap = {
  name: ["Name", "name", "Seller Name"],
  amazon_seller_id: [
    "Amazon Seller ID",
    "amazon_seller_id",
    "Seller ID",
    "amazonSellerId",
    "seller_id",
    "SellerCountryMatching_amazon_seller_id",
  ],
  marketplace: [
    "Marketplace",
    "marketplace",
    "SmartScout Country",
    "smartscout_country",
    "Smartscout Country",
    "Country",
    "country",
  ],
  primary_category: ["Primary Category", "primary_category"],
  primary_sub_category: [
    "Primary Sub Category",
    "primary_sub_category",
    "primarySubCategory",
  ],
  estimate_sales: ["Estimate Sales", "estimate_sales", "estimateSales"],
  avg_price: ["Average Price", "avg_price", "avgPrice"],
  percent_fba: ["Percent FBA", "percent_fba", "percentFba"],
  number_reviews_lifetime: [
    "Number Reviews Lifetime",
    "number_reviews_lifetime",
    "numberReviewsLifetime",
  ],
  number_reviews_30days: [
    "Number Reviews 30 Days",
    "number_reviews_30days",
    "numberReviews30Days",
  ],
  number_winning_brands: [
    "Number Winning Brands",
    "number_winning_brands",
    "numberWinningBrands",
  ],
  number_asins: ["Number ASINs", "number_asins", "numberAsins"],
  number_top_asins: ["Number Top ASINs", "number_top_asins"],
  street: [
    "Street",
    "street",
    "Address",
    "address",
    "sellerAddress",
    "company_address",
  ],
  city: ["City", "city", "Company Location", "company_location"],
  adr_state: ["State", "state"],
  adr_country: ["Country", "country"],
  adr_zip_code: [
    "Zipcode",
    "zipcode",
    "Pincode",
    "pincode",
    "sellerPincode",
    "company_pincode",
  ],
  business_name: ["Business Name", "business_name"],
  number_brands_1000: [
    "Number Brands 1000",
    "number_brands_1000",
    "numberBrands1000",
  ],
  mom_growth: ["MoM Growth", "mom_growth", "moMGrowth"],
  mom_growth_count: ["MoM Growth Count", "mom_growth_count", "moMGrowthCount"],
  is_suspended: ["Is Suspended", "is_suspended", "isSuspended"],
  last_suspended_date: [
    "Last Suspended Date",
    "last_suspended_date",
    "lastSuspendedDate",
  ],
  started_selling_date: [
    "Started Selling Date",
    "started_selling_date",
    "startedSellingDate",
  ],
  domain: (data) => {
    const domainData = data["domain"] || data["Domain"];
    if (domainData && domainData.trim()) {
      return ["Domain", "domain"];
    }
    const websiteData = data["website"] || data["Website"];
    if (websiteData && websiteData.trim()) {
      return ["Website", "website"];
    }
    return ["Domain", "domain"];
  },
  website_status: ["Website Status", "website_status"],
  lookup_source: ["Lookup Source", "lookup_source"],
  seller_url: ["Seller URL", "seller_url", "sellerUrl"],
};

const prospectSchemaMap = {
  amazon_seller_id: ["Amazon Seller ID", "amazon_seller_id", "SellerCountryMatching_amazon_seller_id"],
  domain: ["Domain", "domain"],
  website: ["Website", "company_website", "website", "Company Website Full"],
  person_name: ["Full Name", "full_name", "person_name", "Person Name"],
  person_linkedin: [
    "LinkedIn Link",
    "linkedin_url",
    "person_linkedin",
    "contact LinkedIn",
    "Person Linkedin",
  ],
  apollo_website: [
    "Website",
    "website",
    "Company Website Full",
    "company_website",
    "apollo_website",
    "Apollo Website",
  ],
  phone: ["Phone #", "phone", "Phone"],
  email: ["Email", "eMail", "email"],
  job_title: ["Title", "job_title", "Job Title"],
  contact_location: ["Contact Location", "contact_location", "Contact City"],
  apollo_company_name: [
    "Company Name",
    "company",
    "apollo_company_name",
    "Apollo Company Name",
  ],
  smartscout_country: [
    "SmartScout Country",
    "smartscout_country",
    "Smartscout Country",
  ],
  source: ["Source", "source", "lookup_source", "Lookup Source"],
  email_status: ["Email Status", "email_status", "eMail Status"],
  address: ["Address", "address"],
  pincode: ["Pincode", "pincode"],
};

const amazonProspectSchemaMap = {
  amazon_seller_id: ["Amazon Seller ID", "amazon_seller_id", "amazonSellerId", "SellerCountryMatching_amazon_seller_id"],
  website: ["Website", "company_website", "website", "Company Website Full", "apollo_website", "Apollo Website"],
  domain: ["Domain", "domain"],
  person_name: [
    "Full Name",
    "full_name",
    "person_name",
    "Person Name",
    "Contact Name",
  ],
  person_linkedin: [
    "LinkedIn Link",
    "linkedin_url",
    "person_linkedin",
    "contact LinkedIn",
    "Person Linkedin",
    "contact LinkedIn",
    "LinkedIn URL",
  ],
  email: ["Email", "eMail", "email", "Contact Email"],
  job_title: ["Title", "job_title", "Job Title", "Contact Job Title"],
  source: ["Source", "source", "lookup_source", "Lookup Source", "Data Source"],
  email_status: [
    "Email Status",
    "email_status",
    "eMail Status",
    "Contact Status",
  ],
  seller_id: ["Seller ID", "seller_id", "sellerId"],
};

const matchingSchemaMap = {
  // person_linkedin: ["Prospect LinkedIn URL"],
  // phone: ["Phone #"],
  email: ["Email", "email", "eMail"],
  jeff_output_status: ["Status"],
  client: ["Client"],
  // date_reached_out: ["Date Reached Out"],
};

const leadSchemaMap = {
  sellerName: ["Name", "Seller Name", "name", "sellerName", "company_name"],
  businessName: ["Business Name", "business_name", "businessName"],
  address: ["Company Address", "sellerAddress", "company_address", "address"],
  sellerUrl: [
    "Seller URL",
    "sellerUrl",
    "SellerUrl",
    "seller_url",
    "SellerCountryMatching - Amazon Seller__seller_url",
  ],
  url: ["URL", "url", "website_url", "urls"],
  country: ["Country", "country"],
  metadata: ["metadata"],
  useDomain: ["useDomain"],
};

const storeLeadsSchemaMap = {
  domain: ["tld1", "domain"],
  merchantName: [
    "merchantName",
    "merchantname",
    "merchant_name",
    "seller_name",
    "sellerName",
    "companyName",
  ],
};

const sellerLeadMatchSchemaMap = {
  seller_name: ["Seller Name", "seller_name"],
  seller_url: ["Seller URL", "seller_url"],
  seller_id: ["Seller ID", "amazon_seller_id"],
  client_name: ["Client Name", "client_name"],
  sheet_name: ["Sheet Name", "sheet_name"],
};

async function transformData(data, schemaMap, modelName = null, type = null) {
  // Get the appropriate preprocessor for this data type
  const preprocessor = getPreprocessor(type);

  let processedJson = data;

  // Apply 'before' preprocessing to the raw data
  if (preprocessor?.before) {
    // console.log(`Before preprocessing: ${JSON.stringify(data)}`);
    processedJson = await preprocessor.before(data);
    // console.log(`After preprocessing: ${JSON.stringify(processedJson)}`);
  }

  // Use schema map to transform the data in the order of the schema map
  let transformedData = transformSchema(processedJson, schemaMap);
  // console.log(`Transformed Data: ${JSON.stringify(transformedData)}`);

  // If a preprocessor is provided and it has an 'after' method, run it
  if (preprocessor?.after) {
    // console.log(`Before after: ${JSON.stringify(transformedData)}`);
    await preprocessor.after(transformedData);
    // console.log(`After after: ${JSON.stringify(transformedData)}`);
  }

  if (modelName) {
    transformedData = applyPrismaTypeCasting(transformedData, modelName);
    // console.log(`Type casted Data: ${JSON.stringify(transformedData)}`);
  }

  // If a preprocessor is provided and it has an 'afterPrisma' method, run it
  if (preprocessor?.afterPrisma) {
    // console.log(`Before afterPrisma: ${JSON.stringify(transformedData)}`);
    await preprocessor.afterPrisma(transformedData);
    // console.log(`After afterPrisma: ${JSON.stringify(transformedData)}`);
  }

  return transformedData;
}
// old transform data without preprocessor suppport only meant for some functions which are not refactored/backwards support
function transformDataSync(data, schemaMap, modelName = null) {
  let transformedData = transformSchema(data, schemaMap);

  if (modelName) {
    transformedData = applyPrismaTypeCasting(transformedData, modelName);
  }

  return transformedData;
}

function transformSchema(data, schemaMap) {
  const transformedData = {};
  for (const [key, possibleHeaders] of Object.entries(schemaMap)) {
    const headers =
      typeof possibleHeaders === "function"
        ? possibleHeaders(data)
        : possibleHeaders;
    for (const header of headers) {
      if (data[header] !== undefined) {
        transformedData[key] = data[header];
      }
    }
  }
  return transformedData;
}

function applyPrismaTypeCasting(data, modelName) {
  const transformedData = { ...data };
  for (const key in transformedData) {
    if (key === "errors") continue;

    try {
      transformedData[key] = introspector.typeCastValue(
        modelName,
        key,
        transformedData[key]
      );
    } catch (error) {
      console.warn(`Error type casting ${key}: ${error.message}`);

      if (!transformedData.errors) {
        transformedData.errors = [error.message];
      } else {
        transformedData.errors.push(error.message);
      }
    }
  }
  return transformedData;
}

module.exports = {
  companySchemaMap,
  amazonSellerSchemaMap,
  prospectSchemaMap,
  amazonProspectSchemaMap,
  matchingSchemaMap,
  leadSchemaMap,
  transformData,
  transformDataSync,
  storeLeadsSchemaMap,
  sellerLeadMatchSchemaMap,
};
