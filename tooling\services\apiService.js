const { api, sheets } = require('../config');
const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const { sanitizeObject } = require('../utils/sanitizer');

class APIService {
  constructor() {
    this.baseURL = api.BASE_URL;
    this.client = axios.create({ 
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${api.BEARER_TOKEN}`
      }
    });
  }

  async uploadData(endpoint, data, skipErrorRows = false) {
    try {
      const fullEndpoint = api.ENDPOINTS[endpoint];
      if (!fullEndpoint) {
        throw new Error(`Unknown API endpoint: ${endpoint}`);
      }

      // Sanitize all string values in each row
      const sanitizedData = Array.isArray(data) ? data.map(row => sanitizeObject(row)) : [sanitizeObject(data)];

      // Write to CSV
      const csvWriter = createCsvWriter({
        path: sheets.OUTPUT_CSV_FILE,
        header: Object.keys(sanitizedData[0] || {}).map((key) => ({
          id: key,
          title: key,
        })),
        alwaysQuote: true,
      });

      await csvWriter.writeRecords(sanitizedData);
      console.log(`✅ Transformed CSV file saved: ${sheets.OUTPUT_CSV_FILE}`);

      // Create FormData and append CSV
      const formData = new FormData();
      formData.append("csvFile", fs.createReadStream(sheets.OUTPUT_CSV_FILE));

      const config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${this.baseURL}${fullEndpoint}${skipErrorRows ? '?skiperrorrows=true' : ''}`,
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${api.BEARER_TOKEN}`
        },
        data: formData,
      };

      console.log("📤 Uploading transformed CSV to API...");
      console.log(config.url)
      console.log(config);
      const response = await axios.request(config);

      if (response.status === 200) {
        console.log("✅ Transformed CSV uploaded successfully.");
        return { success: true, data: response.data };
      }

      console.log("❌ Error uploading transformed CSV. Saving response to errors CSV.");
      fs.writeFileSync(sheets.ERRORS_CSV_FILE, response.data);
      return {
        success: false,
        validationFile: sheets.ERRORS_CSV_FILE,
        data: response.data
      };

    } catch (error) {
      if (error.response) {
        if (error.response.status === 403) {
          console.log("ℹ️ Validation response received:");
          const errorCsvContent = error.response.data;
          
          fs.writeFileSync(sheets.ERRORS_CSV_FILE, errorCsvContent);
          console.log(`✅ Validation results saved to ${sheets.ERRORS_CSV_FILE}`);
          
          return {
            success: false,
            validationFile: sheets.ERRORS_CSV_FILE,
            data: error.response.data
          };
        }
        
        console.error(`❌ Upload failed with status ${error.response.status}`);
        console.error("Response:", error.response.data);
        throw error;
      }
      
      console.error(`Error uploading data to ${endpoint}:`, error.message);
      throw error;
    }
  }

  async uploadTransformedData(apiEndpoint, data, skipErrorRows = false) {
    if (!data || data.length === 0) {
      console.error("❌ No data to transform");
      return { success: false, error: "No data to transform" };
    }

    try {
      const response = await this.uploadData(apiEndpoint, data, skipErrorRows);
      return response;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

module.exports = new APIService();
