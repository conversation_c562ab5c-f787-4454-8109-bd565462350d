const fs = require('fs/promises');
const path = require('path');

function extractDomain(url) {
    if (!url || url.toLowerCase() === 'none' || url.toLowerCase() === 'na' || url.toLowerCase() === 'n a') {
        return null;
    }

    // Remove any query parameters and trailing slashes
    url = url.split('?')[0].replace(/\/+$/, '');

    // If it starts with http/https, extract domain
    if (url.startsWith('http')) {
        try {
            const domain = new URL(url).hostname.replace(/^www\./, '');
            return domain;
        } catch (e) {
            return url;
        }
    }

    // If it's just a domain, clean it up
    return url.replace(/^www\./, '');
}

async function findEmptyEnrichments() {
    const data = JSON.parse(await fs.readFile(path.join(__dirname, 'meetingsCRM.json'), 'utf8'));
    
    const emptyEnrichments = data.filter(record => 
        record.SellerBotEnrichedData && 
        Object.keys(record.SellerBotEnrichedData).length === 0
    );
    
    console.log('🌐 Domains with empty enrichment data:');
    console.log('=====================================');
    emptyEnrichments.forEach(record => {
        if (record.website) {
            const domain = extractDomain(record.website);
            if (domain) {
                console.log(domain);
            }
        }
    });
    
    console.log(`\nTotal: ${emptyEnrichments.length} records`);
}

findEmptyEnrichments().catch(console.error);
