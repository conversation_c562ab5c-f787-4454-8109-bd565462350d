// <EMAIL>
require('dotenv').config({ path: '../.env' });

const { sheets } = require("./config");
const funnelConfig = require("./config/funnelConfig");
const prompts = require("prompts");
const googleSheetsService = require("./services/googleSheetsService");
const apiService = require("./services/apiService");
const dataService = require("./services/dataService");
const promptConfig = require("./cli/promptConfig");

// Run the functions
(async () => {
  try {
    const response = await prompts(promptConfig);

    if (response.operationType === "maintenance") {
      const sheetNames = response.subSheetsToProcess.split(",").map(s => s.trim());
      
      if (response.maintenanceType === "delete") {
        console.log("🗑️ Deleting sub-sheets...");
        await googleSheetsService.deleteSubSheets(sheets.MANUAL_SHEET_ID, sheetNames);
      } else if (response.maintenanceType === "hide") {
        console.log("👻 Hiding sub-sheets...");
        await googleSheetsService.hideSubSheets(sheets.MANUAL_SHEET_ID, sheetNames);
      }
      return;
    }

    const sheetId = response.sheetOption === "url" 
      ? response.sheetUrl.match(/spreadsheets\/d\/([a-zA-Z0-9-_]+)/)[1]
      : response.sheetOption;

    // Get sheet names based on input type
    const sheetNames = response.sheetOption === "url"
      ? response.urlSheetName.split(",").map(s => s.trim())
      : response.sheetNames.split(",").map(s => s.trim());
    
    // Get and validate funnel configuration
    const selectedFunnel = funnelConfig[response.funnel];
    
    if (!selectedFunnel) {
      throw new Error(`Invalid funnel selected: ${response.funnel}. Please choose a valid funnel.`);
    }
    
    console.log(`Selected funnel: ${response.funnel}`);
    
    // Determine API endpoint and mode based on funnel selection
    let apiEndpoint;
    let mode;
    let funnelLookupSource;
    
    // Check if there's a sub-funnel selection that might override the config
    if (response.subFunnel && selectedFunnel.subFunnels && selectedFunnel.subFunnels[response.subFunnel]) {
      const subFunnelConfig = selectedFunnel.subFunnels[response.subFunnel];
      console.log(`Selected sub-funnel: ${response.subFunnel}`);
      
      // Use sub-funnel config to override main funnel settings if specified
      apiEndpoint = subFunnelConfig.apiEndpoint || selectedFunnel.apiEndpoint;
      mode = subFunnelConfig.mode || selectedFunnel.mode;
      funnelLookupSource = subFunnelConfig.lookupSource || selectedFunnel.lookupSource;
    } else {
      // Use main funnel config
      apiEndpoint = selectedFunnel.apiEndpoint;
      mode = selectedFunnel.mode;
      funnelLookupSource = selectedFunnel.lookupSource;
    }
    
    // Validate that we have the required config values
    if (!apiEndpoint) {
      throw new Error(`No API endpoint defined for funnel: ${response.funnel}`);
    }
    
    if (!mode) {
      throw new Error(`No processing mode defined for funnel: ${response.funnel}`);
    }
    
    console.log(`Using API endpoint: ${apiEndpoint}`);
    console.log(`Using processing mode: ${mode}`);

    // Process each sheet
    for (const sheetName of sheetNames) {
      console.log(`\nProcessing sheet: ${sheetName}`);
      
      // We're no longer asking for user-provided lookup source
      // All lookup sources are determined from funnel configuration
      // This includes lookup sources based on website_status and default funnel lookup sources
      const lookupSource = null; // Set to null to ensure data service uses only funnel config sources

      // Download and process the sheet
      const data = await googleSheetsService.downloadAsCSV(sheetId, sheetName, response.ignoreErrors);
      if (!data) {
        console.log(`❌ Failed to process sheet: ${sheetName}`);
        continue;
      }

      console.log(`Processing with mode: ${mode}`);
      // Pass both funnel name and subfunnel name (if available) to the processData method
      const processedData = await dataService.processData(
        data, 
        lookupSource, 
        mode, 
        response.funnel, 
        response.subFunnel || null
      );

      // Create a simple formatted preview
      console.log(`

======= PROCESSED DATA PREVIEW =======`);
      console.log(`Funnel: ${response.funnel}`);
      console.log(`API Endpoint: ${apiEndpoint}`);
      console.log(`Processing Mode: ${mode}`);
      console.log(`Sheet: ${sheetName}`);
      console.log(`
🔍 First 3 records preview:`);
      
      // Show first 3 records for preview
      const previewData = processedData.slice(0, 3);
      console.log(JSON.stringify(previewData, null, 2));
      
      console.log(`
Total Records: ${processedData.length}`);
      console.log(`========================================
`);
      
      // Prompt for confirmation before proceeding with upload
      const confirmUpload = await prompts({
        type: 'confirm',
        name: 'upload',
        message: 'Do you want to proceed with API upload?',
        initial: false // Default to not uploading for safety
      });
      
      // Only upload if confirmed
      if (confirmUpload.upload) {
        console.log(`📤 Uploading data for sheet: ${sheetName}`);
        const uploadResults = await apiService.uploadTransformedData(
          apiEndpoint, // Use the determined API endpoint from funnel config
          processedData,
          response.skipErrorRows
        );

        if (uploadResults.success) {
          console.log(`✅ Upload completed successfully for sheet: ${sheetName}!`);
        } else {
          console.log("uploadResults", uploadResults);
          if (uploadResults.validationFile) {
            console.log(`❌ Upload failed for sheet: ${sheetName} - validation errors found`);
            console.log(`Validation results saved to: ${uploadResults.validationFile}`);
            
            // Upload errors to Google Sheet
            console.log("📤 Uploading errors to Google Sheet...");
            await googleSheetsService.uploadErrorsToSheet(uploadResults.validationFile);
            console.log("✅ Errors uploaded to Google Sheet");
          } else {
            console.log(`❌ Upload failed for sheet: ${sheetName}:`, uploadResults.error);
          }
        }
      } else {
        console.log('⏸️ API upload skipped. Data processing completed successfully for testing.');
        // Also save the data to a JSON file for further inspection
        const fs = require('fs');
        const outputPath = './processed_data_preview.json';
        fs.writeFileSync(outputPath, JSON.stringify(processedData, null, 2));
        console.log(`💾 Full processed data saved to ${outputPath} for inspection`);
      }
    }
  } catch (error) {
    console.error("❌ An error occurred:", error.message);
    throw error;
  }
})();
