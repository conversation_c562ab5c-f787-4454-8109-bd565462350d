const { google } = require('googleapis');
const { sheets } = require('../config');
const fs = require('fs');
const csv = require('csv-parse');
const { promisify } = require('util');

class GoogleSheetsService {
  constructor() {
    this.auth = null;
    this.sheets = null;
    this.initialized = false;
    this.config = null;
  }

  async initialize(config) {
    if (this.initialized && !config) return;

    const auth = new google.auth.GoogleAuth({
      keyFile: sheets.CREDENTIALS_PATH,
      scopes: sheets.SCOPES,
    });

    this.auth = auth;
    this.sheets = google.sheets({ version: 'v4', auth });
    this.initialized = true;

    if (config) {
      this.config = config;
    }
  }

  async _getSheetId(spreadsheetId, sheetName) {
    const response = await this.sheets.spreadsheets.get({
      spreadsheetId,
      fields: 'sheets.properties'
    });

    const sheet = response.data.sheets.find(s => s.properties.title === sheetName);
    return sheet ? sheet.properties.sheetId : null;
  }

  async hideSheet(spreadsheetId, sheetName) {
    try {
      await this.initialize();
      const sheetId = await this._getSheetId(spreadsheetId, sheetName);
      
      if (!sheetId) {
        console.log(`⚠️ Sheet "${sheetName}" not found`);
        return false;
      }

      await this.sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: {
          requests: [{
            updateSheetProperties: {
              properties: {
                sheetId: sheetId,
                hidden: true
              },
              fields: 'hidden'
            }
          }]
        }
      });

      console.log(`✅ Successfully hidden sheet: ${sheetName}`);
      return true;
    } catch (error) {
      console.error(`❌ Error hiding sheet ${sheetName}:`, error.message);
      return false;
    }
  }

  async deleteSubSheets(spreadsheetId, sheetNames) {
    try {
      await this.initialize();
      
      for (const sheetName of sheetNames) {
        const sheetId = await this._getSheetId(spreadsheetId, sheetName);
        
        if (!sheetId) {
          console.log(`⚠️ Sheet "${sheetName}" not found`);
          continue;
        }

        await this.sheets.spreadsheets.batchUpdate({
          spreadsheetId,
          requestBody: {
            requests: [{
              deleteSheet: {
                sheetId: sheetId
              }
            }]
          }
        });

        console.log(`✅ Successfully deleted sheet: ${sheetName}`);
      }
      return true;
    } catch (error) {
      console.error("❌ Error deleting sheets:", error.message);
      return false;
    }
  }

  async hideSubSheets(spreadsheetId, sheetNames) {
    try {
      await this.initialize();
      
      for (const sheetName of sheetNames) {
        await this.hideSheet(spreadsheetId, sheetName);
      }
      return true;
    } catch (error) {
      console.error("❌ Error hiding sheets:", error.message);
      return false;
    }
  }

  async downloadAsCSV(sheetId, sheetName, ignoreErrors = false) {
    try {
      await this.initialize();

      console.log(`📥 Downloading sheet: ${sheetName}`);
      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: sheetId,
        range: sheetName,
      });

      const rows = response.data.values;
      if (!rows || rows.length === 0) {
        console.error("❌ No data found in sheet");
        return null;
      }

      const rawHeaders = rows[0];

      // Handle duplicate headers by keeping first occurrence
      const usedHeaders = new Set();
      const headers = rawHeaders.map((header, index) => {
        if (header === "Website Status" && usedHeaders.has(header)) {
          console.log(`⚠️ Found duplicate 'Website Status' header at index ${index}, ignoring it`);
          return `${header}_DUPLICATE_${index}`; // Mark as duplicate to ignore later
        }
        usedHeaders.add(header);
        return header;
      });

      let data = rows.slice(1).map(row => {
        const obj = {};
        headers.forEach((header, i) => {
          // Only add the value if it's not from a duplicate Website Status column
          if (!header.includes('Website Status_DUPLICATE_')) {
            obj[header] = row[i] || '';
          }
        });
        return obj;
      });

      // If this is the errors sheet and ignoreErrors is true,
      // filter out rows where errors is not "undefined"
      if (sheetId === sheets.ERRORS_SHEET_ID && ignoreErrors) {
        data = data.filter(row => row.errors === "undefined");
        console.log(`ℹ️ Filtered out rows with errors, ${data.length} rows remaining`);
      }

      console.log(`✅ Downloaded ${data.length} rows`);
      return data;
    } catch (error) {
      console.error("❌ Error downloading sheet:", error.message);
      return null;
    }
  }

  async getSheetData() {
    try {
      await this.initialize();

      if (!this.config || !this.config.spreadsheetId || !this.config.sheetName) {
        throw new Error('Configuration missing spreadsheetId or sheetName');
      }

      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: this.config.spreadsheetId,
        range: `${this.config.sheetName}!A:Z`, // Get all columns
      });

      const rows = response.data.values;
      if (!rows || rows.length === 0) {
        throw new Error('No data found in the sheet');
      }

      const headers = rows[0];
      const data = rows.slice(1).map(row => {
        const obj = {};
        headers.forEach((header, i) => {
          obj[header] = row[i] || '';
        });
        return obj;
      });

      return data;
    } catch (error) {
      console.error('Error fetching sheet data:', error.message);
      throw error;
    }
  }

  async uploadCSVToSheet(csvPath, spreadsheetId, sheetName = 'Sheet1') {
    try {
      await this.initialize();

      // Read the CSV file with proper parsing
      const parseCSV = promisify(csv.parse);
      const fileContent = await fs.promises.readFile(csvPath, 'utf-8');
      const records = await parseCSV(fileContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
        bom: true
      });

      if (records.length === 0) {
        console.log("ℹ️ No data to upload");
        return;
      }

      // Get headers from the first record
      const headers = Object.keys(records[0]);

      // Prepare values array with headers as first row
      const values = [
        headers,
        ...records.map(record => headers.map(header => record[header] || ''))
      ];

      // Clear existing content first
      await this.sheets.spreadsheets.values.clear({
        spreadsheetId,
        range: sheetName,
      });

      // Upload the new values
      await this.sheets.spreadsheets.values.update({
        spreadsheetId,
        range: sheetName,
        valueInputOption: 'USER_ENTERED',
        resource: { values },
      });

      return records.length;
    } catch (error) {
      console.error("❌ Error uploading CSV to sheet:", error.message);
      throw error;
    }
  }

  async uploadErrorsToSheet(errorsCsvPath) {
    try {
      const rowCount = await this.uploadCSVToSheet(errorsCsvPath, sheets.ERRORS_SHEET_ID, 'Sheet1');
      if (rowCount) {
        console.log(`✅ Uploaded ${rowCount} rows to Google Sheet`);
      } else {
        console.log("ℹ️ No errors to upload");
      }
    } catch (error) {
      console.error("❌ Error uploading errors to sheet:", error.message);
      throw error;
    }
  }
}

module.exports = new GoogleSheetsService();
