const express = require("express");
const cors = require("cors");
// require("./services/saveEmails/index");
const jobWorker = require("./worker/jobWorker");
const reviewUpdater = require("./services/lexReviewsChecker/reviewUpdater");
const reviewChecker = require("./services/lexReviewsChecker/reviewChecker");
const {
  emailThreadUpdater,
} = require("./services/saveEmails/emailThreadUpdater");
const {
  startWorkerWithCron,
} = require("./services/generateLeads/workers/jobUpdateWorker");

require("dotenv").config();
require("newrelic");

const { specs, swaggerUi } = require("./swagger");
const path = require("path");

const app = express();
const PORT = 8000;

// Middleware
app.use(
  cors({
    origin: "*",
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
    preflightContinue: false,
    optionsSuccessStatus: 204,
    exposedHeaders: ["Content-Type", "Authorization", "Content-Disposition"],
  })
);
app.use(express.json());
// app.use(newrelic.expressMiddleware());
app.use(
  "/api-docs",
  (req, res, next) => {
    const protocol = req.protocol;
    const host = req.get("host");

    specs.servers = [
      {
        url: `${protocol}://${host}`,
        description: "Current server",
      },
    ];
    next();
  },
  swaggerUi.serve,
  swaggerUi.setup(specs, { explorer: true })
);
app.use("/examples", express.static(path.join(__dirname, "public/examples")));
// Import routes
const routes = require("./routes");
const lexRoutes = require("./routes/lex");
app.use(routes);
app.use(lexRoutes);

//LeadJob update and slack Notification worker
// startWorkerWithCron();

app.listen(PORT, () =>
  console.log(`Server running on http://localhost:${PORT}`)
);
