/**
 * Generate search string based on the selected pattern
 * @param {string} sellerName - The seller name
 * @param {string} businessName - The business name
 * @param {string} pattern - The search pattern to use
 * @param {Object} leadData - Complete lead data for custom patterns (optional)
 * @returns {string} - The generated search string
 */
function generateSearchString(
  sellerName,
  businessName,
  pattern = "original",
  leadData = {}
) {
  const seller = sellerName || "";
  const business = businessName || "";

  // Check if it's a custom pattern (contains {{ }})
  if (pattern && pattern.includes("{{") && pattern.includes("}}")) {
    return generateCustomSearchString(pattern, {
      sellerName: seller,
      businessName: business,
      ...leadData,
    });
  }

  switch (pattern) {
    case "quoted_with_shop":
      // "sellerName" | "bizName" shop
      return `"${seller}" | "${business}" shop`.trim();

    case "unquoted_with_shop":
      // sellerName bizName shop
      return `${seller} ${business} shop`.trim();

    case "original":
    default:
      // Original query: sellerName businessName
      return `${seller} ${business}`.trim();
  }
}

/**
 * Generate search string from custom pattern with placeholders
 * @param {string} customPattern - Pattern with {{columnName}} placeholders
 * @param {Object} leadData - Lead data to replace placeholders
 * @returns {string} - Generated search string
 */
function generateCustomSearchString(customPattern, leadData) {
  let searchString = customPattern;

  // Replace all {{columnName}} placeholders with actual values
  const placeholderRegex = /\{\{([^}]+)\}\}/g;

  searchString = searchString.replace(placeholderRegex, (match, columnName) => {
    const trimmedColumnName = columnName.trim();
    const value = leadData[trimmedColumnName];

    // Return the value if it exists, otherwise return empty string
    return value || "";
  });

  // Clean up extra spaces
  return searchString.replace(/\s+/g, " ").trim();
}

/**
 * Get available search patterns
 * @returns {Array} - Array of available search patterns with descriptions
 */
function getAvailableSearchPatterns() {
  return [
    {
      value: "original",
      label: "Original (sellerName businessName)",
      description:
        "The original search pattern using seller name and business name",
    },
    {
      value: "quoted_with_shop",
      label: 'Quoted with Shop ("sellerName" | "businessName" shop)',
      description:
        "Searches for exact phrases with OR operator and includes 'shop' keyword",
    },
    {
      value: "unquoted_with_shop",
      label: "Unquoted with Shop (sellerName businessName shop)",
      description:
        "Searches for seller name, business name and 'shop' without quotes",
    },
    {
      value: "custom",
      label: "Custom Pattern",
      description:
        "Use custom search pattern with {{columnName}} placeholders. Example: '{{sellerName}} {{businessName}} shop' or '\"{{sellerName}}\" | \"{{businessName}}\" {{country}}'",
    },
  ];
}

/**
 * Get available column names that can be used in custom patterns
 * @returns {Array} - Array of available column names
 */
function getAvailableColumns() {
  return [
    "sellerName",
    "businessName",
    "address",
    "country",
    "sellerUrl",
    "metadata",
  ];
}

module.exports = {
  generateSearchString,
  getAvailableSearchPatterns,
  getAvailableColumns,
};
