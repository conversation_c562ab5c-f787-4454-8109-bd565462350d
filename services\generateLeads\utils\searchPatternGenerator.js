/**
 * Generate search string based on the selected pattern
 * @param {string} sellerName - The seller name
 * @param {string} businessName - The business name  
 * @param {string} pattern - The search pattern to use
 * @returns {string} - The generated search string
 */
function generateSearchString(sellerName, businessName, pattern = "original") {
  const seller = sellerName || "";
  const business = businessName || "";
  
  switch (pattern) {
    case "quoted_with_shop":
      // "sellerName" | "bizName" shop
      return `"${seller}" | "${business}" shop`.trim();
      
    case "unquoted_with_shop":
      // sellerName bizName shop
      return `${seller} ${business} shop`.trim();
      
    case "original":
    default:
      // Original query: sellerName businessName
      return `${seller} ${business}`.trim();
  }
}

/**
 * Get available search patterns
 * @returns {Array} - Array of available search patterns with descriptions
 */
function getAvailableSearchPatterns() {
  return [
    {
      value: "original",
      label: "Original (sellerName businessName)",
      description: "The original search pattern using seller name and business name"
    },
    {
      value: "quoted_with_shop", 
      label: 'Quoted with Shop ("sellerName" | "businessName" shop)',
      description: "Searches for exact phrases with OR operator and includes 'shop' keyword"
    },
    {
      value: "unquoted_with_shop",
      label: "Unquoted with Shop (sellerName businessName shop)", 
      description: "Searches for seller name, business name and 'shop' without quotes"
    }
  ];
}

module.exports = {
  generateSearchString,
  getAvailableSearchPatterns
};
