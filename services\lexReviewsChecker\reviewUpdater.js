const prisma = require("../../database/prisma/getPrismaClient");
const cron = require("node-cron");
const { processReview, sendLexSlackNotification } = require("./utils");
const { ReviewJobStatus, JobStatus } = require("@prisma/client");
const { ReviewStatus } = require("@prisma/client");
require("dotenv").config();

// Add a global flag to track if a job is in progress
let isProcessing = false;

const Bottleneck = require("bottleneck");

const limiter = new Bottleneck({
  maxConcurrent: 30, // Adjust based on your system capacity
  minTime: 100, // At most 10 requests per second
});

reviewUpdater();
cron.schedule("* * * * *", reviewUpdater);

async function reviewUpdater() {
  console.log("Processing Review Updater Job");

  const processingJob = await prisma.reviewJob.findFirst({
    where: {
      status: ReviewJobStatus.PROCESSING,
    },
  });
  if (processingJob?.id) {
    isProcessing = true;
  }
  // If already processing a job, don't start another one
  if (isProcessing) {
    console.log("A job is already being processed, skipping this cycle");
    return;
  }

  try {
    isProcessing = true;

    // Reset stuck jobs
    await prisma.reviewJob.updateMany({
      where: {
        status: ReviewJobStatus.PROCESSING,
        updatedAt: {
          lt: new Date(Date.now() - 12 * 60 * 60 * 1000),
        },
      },
      data: {
        status: ReviewJobStatus.PENDING,
      },
    });

    // Find the oldest queued job
    const job = await prisma.reviewJob.findFirst({
      where: { status: ReviewJobStatus.PENDING },
      orderBy: { createdAt: "asc" },
    });

    if (!job) {
      isProcessing = false;
      return; // No jobs to process
    }

    // Update job status to 'processing'
    await prisma.reviewJob.update({
      where: { id: job.id },
      data: {
        status: ReviewJobStatus.PROCESSING,
      },
    });

    try {
      // const csvBuffer = await reviewChecker(job.inputFilePath);
      const pendingReviews = await prisma.reviewOutputData.findMany({
        where: {
          reviewJobId: job.id,
          status: {
            in: [ReviewStatus.PENDING, ReviewStatus.FAILED],
          },
        },
        select: {
          id: true,
          revId: true,
          review: true,
        },
      });
      const tasks = pendingReviews.map((review) =>
        limiter.schedule(async () => {
          try {
            const reviewStatus = await processReview(review.review.reviewUrl);
            console.log("Review Status:", reviewStatus);
            console.log("Previous Review Status:", review.review.status);

            if (
              reviewStatus === ReviewStatus.REMOVED &&
              (review.review.status === ReviewStatus.PRESENT ||
                review.review.status === ReviewStatus.PENDING)
            ) {
              await sendLexSlackNotification(review.review);
            }

            await prisma.review.update({
              where: { id: review.revId },
              data: { status: reviewStatus },
            });

            await prisma.reviewOutputData.update({
              where: { id: review.id },
              data: { status: reviewStatus },
            });

            console.log(`Processed review ${review.id}: ${reviewStatus}`);
          } catch (error) {
            console.error(`Error processing review ${review.id}:`, error);
             await prisma.reviewOutputData.update({
               where: { id: review.id },
               data: { status: ReviewStatus.FAILED },
             });
          }
        })
      );

      await Promise.all(tasks);

      // Update job with results
      const remaining = await prisma.reviewOutputData.count({
        where: {
          reviewJobId: job.id,
          status: {
            in: [ReviewStatus.PENDING, ReviewStatus.FAILED],
          },
        },
      });

      await prisma.reviewJob.update({
        where: { id: job.id },
        data: {
          status:
            remaining > 0 ? ReviewJobStatus.FAILED : ReviewJobStatus.COMPLETED,
        },
      });

      console.log(`Job ${job.id} completed successfully`);
    } catch (error) {
      await prisma.reviewJob.update({
        where: { id: job.id },
        data: {
          status: ReviewJobStatus.FAILED,
        },
      });
      console.error(`Job ${job.id} failed:`, error);
    }
  } catch (error) {
    console.error("Error in job processor:", error);
    prisma.reviewJob
      .update({
        where: { id: job.id },
        data: {
          status: ReviewJobStatus.FAILED,
        },
      })
      .catch((err) => {
        console.error("Error updating job status:", err.message);
      });
  } finally {
    // Make sure to reset the flag when done, regardless of success or failure
    isProcessing = false;
  }
}

module.exports = { reviewUpdater };
