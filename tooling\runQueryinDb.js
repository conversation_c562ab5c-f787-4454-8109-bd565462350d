require("dotenv").config({ path: "../.env" });
const prisma = require("../database/prisma/getPrismaClient");

const DEFAULT_QUERY = `SELECT   COUNT(*) AS "count" FROM   "public"."Company"`;
async function runQuery(query) {
  try {
    const result = await prisma.$queryRawUnsafe(query);
    return result;
  } catch (error) {
    console.error("Error executing query:", error);
    throw error;
  }
}

(async () => {
  const result = await runQuery(DEFAULT_QUERY);
  console.log(result);
})();