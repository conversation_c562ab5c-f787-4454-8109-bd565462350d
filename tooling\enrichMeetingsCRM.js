const fsPromises = require('fs/promises');
const fsSync = require('fs');
const path = require('path');
const { enrichMeetingsCRMData } = require('./services/enrichmentService');
const googleSheetsService = require('./services/googleSheetsService');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

// Helper function to sanitize string values
function sanitizeValue(value) {
    return typeof value === 'string' ? value.trim().replace(/[\r\n\s]+/g, ' ').trim() : '';
}

async function processData(config) {
    try {
        await googleSheetsService.initialize(config);
        const rows = await googleSheetsService.getSheetData();
        if (!rows || rows.length === 0) {
            throw new Error('No data found in the sheet');
        }

        // Process each row according to schema requirements
        const processedData = rows.map(row => {
            const processed = {
                client: sanitizeValue(row['Client']),
                slot_sent_date: sanitizeValue(row['Slot Sent Date']),
                week: sanitizeValue(row['Week']),
                meeting_booked_date: sanitizeValue(row['Meeting Booked Date']),
                email_1: sanitizeValue(row['eMail 1']),
                email_2: sanitizeValue(row['eMail 2']),
                seller_name: sanitizeValue(row['Seller Name']),
                website: sanitizeValue(row['Website']),
                jeff_status: sanitizeValue(row['Jeff Status']),
                client_status: sanitizeValue(row['Client Status']),
                invoice_status: sanitizeValue(row['Invoice Status']),
                SellerBotEnrichedData: {} // Always include this object
            };
            return processed;
        });

        // Log records with missing seller names but keep all records
        console.log('\n🔍 Checking records...');
        processedData.forEach((row, index) => {
            if (!row.seller_name) {
                console.log(`⚠️ Row ${index + 1} missing name (checked 'Seller Name' and 'Name')`);
            }
        });

        console.log(`\n📊 Processing summary:`);
        console.log(`Total rows: ${processedData.length}`);

        return processedData;
    } catch (error) {
        console.error('Error processing data:', error.message);
        throw error;
    }
}

async function exportToCSV(data) {
    const csvPath = path.join(__dirname, 'meetingsCRM_export.csv');
    const csvWriter = createCsvWriter({
        path: csvPath,
        header: [
            {id: 'client', title: 'client'},
            {id: 'slot_sent_date', title: 'slot_sent_date'},
            {id: 'meeting_booked_date', title: 'meeting_booked_date'},
            {id: 'email_1', title: 'email_1'},
            {id: 'email_2', title: 'email_2'},
            {id: 'seller_name', title: 'seller_name'},
            {id: 'website', title: 'website'},
            {id: 'jeff_status', title: 'jeff_status'},
            {id: 'client_status', title: 'client_status'},
            {id: 'invoice_status', title: 'invoice_status'},
            {id: 'amazon_seller_id', title: 'amazon_seller_id'},
            {id: 'jeff_search_priority', title: 'jeff_search_priority'},
            {id: '_enriched_by', title: '_enriched_by'},
            {id: 'week', title: 'week'}
        ]
    });

    // Transform data to include enriched fields at top level
    const flattenedData = data.map(record => ({
        ...record,
        amazon_seller_id: record.SellerBotEnrichedData.amazon_seller_id || '',
        jeff_search_priority: record.SellerBotEnrichedData.jeff_search_priority || '',
        _enriched_by: record.SellerBotEnrichedData._enriched_by ? record.SellerBotEnrichedData._enriched_by.join(',') : ''
    }));

    await csvWriter.writeRecords(flattenedData);
    return csvPath;
}

async function main() {
    const mode = process.argv[2] || '1';
    console.log(`🚀 Running in mode ${mode}`);

    try {
        let data;
        const outputPath = path.join(__dirname, 'meetingsCRM.json');
        const config = {
            spreadsheetId: '1zMHv7jJUhz3kwuBAUxVXtTlrmQ2TY1g_OyqU2eRxtks',
            sheetName: 'Meetings CRM',
        };

        switch(mode) {
            case '1':
                // Mode 1: Fetch fresh data from Google Sheets and enrich
                console.log('📊 Fetching data from Google Sheets...');
                data = await processData(config);
                data = await enrichMeetingsCRMData(data);
                await fsPromises.writeFile(outputPath, JSON.stringify(data, null, 2));
                break;

            case '2':
                // Mode 2: Read from existing JSON and re-enrich all
                console.log('📖 Reading from existing JSON file...');
                data = JSON.parse(await fsPromises.readFile(outputPath, 'utf8'));
                data = await enrichMeetingsCRMData(data);
                await fsPromises.writeFile(outputPath, JSON.stringify(data, null, 2));
                break;

            case '3':
                // Mode 3: Read from JSON and enrich only records with empty enrichment data using domains
                console.log('🌐 Reading from JSON and enriching empty records using domains...');
                data = JSON.parse(await fsPromises.readFile(outputPath, 'utf8'));
                
                // Filter records with empty enrichment data and valid websites
                const emptyRecords = data.filter(record => 
                    record.website && 
                    (!record.SellerBotEnrichedData || Object.keys(record.SellerBotEnrichedData).length === 0)
                );
                
                console.log(`Found ${emptyRecords.length} records with empty enrichment data and websites`);
                
                // Create a subset of records with only website for domain-based enrichment
                const domainRecords = emptyRecords.map(record => ({
                    ...record,
                    email_1: '', // Clear email to force domain-based enrichment
                }));
                
                // Enrich only the empty records
                const enrichedEmptyRecords = await enrichMeetingsCRMData(domainRecords);
                
                // Merge back into the original data
                const enrichedMap = new Map(
                    enrichedEmptyRecords.map(record => [record.website, record.SellerBotEnrichedData])
                );
                
                data = data.map(record => {
                    if (record.website && enrichedMap.has(record.website)) {
                        return {
                            ...record,
                            SellerBotEnrichedData: enrichedMap.get(record.website)
                        };
                    }
                    return record;
                });
                
                await fsPromises.writeFile(outputPath, JSON.stringify(data, null, 2));
                break;

            case '4':
                // Mode 4: Export enriched data to CSV
                console.log('📊 Reading JSON and exporting to CSV...');
                data = JSON.parse(await fsPromises.readFile(outputPath, 'utf8'));
                await exportToCSV(data);
                break;

            case '5':
                // Mode 5: Upload exported CSV to Google Sheet
                console.log('📤 Uploading CSV to Google Sheet...');
                const csvPath = path.join(__dirname, 'meetingsCRM_export.csv');
                if (!fsSync.existsSync(csvPath)) {
                    throw new Error('CSV file not found. Please run mode 4 first to generate the CSV.');
                }
                await googleSheetsService.uploadCSVToSheet(
                    csvPath,
                    config.spreadsheetId,
                    'Enriched CRM'
                );
                console.log('✅ CSV data uploaded to "Enriched CRM" sheet');
                break;

            default:
                console.log('❌ Invalid mode. Please use 1, 2, 3, 4, or 5');
                process.exit(1);
        }

        console.log('✅ Process completed successfully!');
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
}

main();
