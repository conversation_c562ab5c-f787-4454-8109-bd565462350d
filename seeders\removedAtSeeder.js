const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

async function run() {
  try {
    const reviews = await prisma.review.findMany({
      select: { id: true, updatedAt: true },
    });

    const batchSize = 100; // Tune based on your DB's performance
    for (let i = 0; i < reviews.length; i += batchSize) {
      const batch = reviews.slice(i, i + batchSize);

      await Promise.all(
        batch.map((review) =>
          prisma.review.update({
            where: { id: review.id },
            data: { removedAt: review.updatedAt },
          })
        )
      );

      console.log(`Processed ${i + batch.length} / ${reviews.length}`);
    }

    console.log(`✅ Updated ${reviews.length} reviews`);
  } catch (error) {
    console.error("❌ ERROR:", error);
  } finally {
    await prisma.$disconnect();
  }
}

run();
