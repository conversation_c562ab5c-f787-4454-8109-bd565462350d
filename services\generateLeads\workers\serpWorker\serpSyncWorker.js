const prisma = require("../../../../database/prisma/getPrismaClient");
const { getRelevantResultsSnippet } = require("../../SERP/getSerpRequest");

const WORKER_PROCESS_ID = `sync_serp_worker_${Date.now()}_${Math.random()
  .toString(36)
  .substr(2, 9)}`;

async function processSyncRequests() {
  // First find active sync SERP jobs
  console.log("Finding active sync SERP jobs...");
  const activeSyncJobs = await prisma.leadJob.findMany({
    where: {
      asyncSerp: false,
      status: "pending",
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  if (activeSyncJobs.length === 0) {
    return null;
  }

  const activeJobIds = activeSyncJobs.map((job) => job.id);
  console.log(activeJobIds);
  console.log(`Found ${activeSyncJobs.length} active sync/csv jobs`);
  // Get batch of pending leads for active sync jobs
  const pendingLeads = await prisma.$transaction(async (tx) => {
    const leads = await tx.lead.findMany({
      where: {
        status: "pending",
        processId: null,
        jobId: {
          in: activeJobIds,
        },
      },
      take: 5,
      orderBy: {
        createdAt: "asc",
      },
    });

    if (leads.length > 0) {
      await tx.lead.updateMany({
        where: {
          id: {
            in: leads.map((l) => l.id),
          },
        },
        data: {
          processId: WORKER_PROCESS_ID,
          status: "processing",
        },
      });
    }
    return leads;
  });

  if (pendingLeads.length > 0) {
    try {
      await Promise.all(
        pendingLeads.map(async (lead) => {
          const searchString = `${lead.sellerName} ${lead.businessName}`.trim();
          console.log(
            `Processing lead ${lead.id} with search string ${searchString}`,
          );
          const serpResult = await getRelevantResultsSnippet(
            searchString,
            lead.country,
          );
          console.log(
            `Got SERP result for lead ${lead.id}: ${JSON.stringify(serpResult)}`,
          );

          // Use a ternary operator to determine the status based on the presence of organic results
          const status =
            serpResult?.results?.results?.organic &&
            serpResult.results.results.organic.length > 0
              ? "srp_scraped"
              : "srp_failed";

          // Update the lead status based on the ternary operation result
          await prisma.lead.update({
            where: { id: lead.id },
            data: {
              status: status,
              apiResponse: status === "srp_scraped" ? serpResult : null,
              processId: null,
            },
          });
        }),
      );

      return { success: true, batchSize: pendingLeads.length };
    } catch (error) {
      console.error(
        `Error processing leads for sync SERP jobs: ${error.message}`,
      );
      console.error(error.stack);
      for (const lead of pendingLeads) {
        await prisma.lead.update({
          where: { id: lead.id },
          data: {
            status: "pending",
            processId: null,
          },
        });
      }
      return { success: false, error: error.message };
    }
  }

  return null;
}
processSyncRequests();
module.exports = processSyncRequests;
