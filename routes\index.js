const express = require("express");
const router = express.Router();
// const { runJobUpdateWorker } = require("../services/generateLeads/workers/jobUpdateWorker");

// Import user routes
const userRoutes = require("./user");
router.use(userRoutes);

// Import job routes
const jobRoutes = require("./job");
router.use(jobRoutes);

// Import validation routes
const validationRoutes = require("./lead");
router.use(validationRoutes);

//Adhoc routes
const adhocRoutes = require("./adHoc");
router.use(adhocRoutes);

// Import FuzzySearch routes
const fuzzySearch = require("./fuzzysearch");
router.use(fuzzySearch);

// Import DNS Records routes
const dnsRecords = require("./DnsRecords");
router.use(dnsRecords);

const smartleads = require("./smartleads");
router.use(smartleads);

// Import Metabase CSV routes
const metabaseCSV = require("./metabaseCSV");
router.use(metabaseCSV);

module.exports = router;
